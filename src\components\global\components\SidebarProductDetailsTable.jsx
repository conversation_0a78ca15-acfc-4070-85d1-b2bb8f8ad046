import { Popover, Typography } from 'antd';
import { useEffect, useState } from 'react';
import { FaLock } from 'react-icons/fa';
import { Tooltip as ReactTooltip } from 'react-tooltip';
import {
  addCommaToAmount,
  getCorrespondingConversionFactor,
  getDecodedHTML,
} from '../../../helperFunction';
import { useLazyGetMediaByIdQuery } from '../../../slices/mediaSlice';
import { useGetAllProductsForOptionsQuery } from '../../../slices/productApiSlice';
import {
  DEFAULT_PO_PRODUCT_DETAILS_HEADER,
  QUOTATION_FIELDS,
  SALES_ORDER_FIELDS,
} from '../../../utils/Constant';
import { useGetHeaderByPageQuery } from '../../../slices/headerReorderApiSlice';
const { Title } = Typography;

const ShowMedia = (id) => {
  const [getData, { data }] = useLazyGetMediaByIdQuery();

  useEffect(() => {
    if (id) getData(id);
  }, [id, getData]);
  return (
    <Popover
      content={
        <img
          src={data?.media?.data}
          alt="Product"
          style={{
            maxWidth: '300px',
            maxHeight: '300px',
          }}
        />
      }
      trigger="hover"
      className="z-[10000]"
    >
      <img
        src={data?.media?.data}
        alt="Product"
        className="mr-2 cursor-pointer"
        style={{
          width: '30px',
          height: '30px',
          objectFit: 'cover',
        }}
      />
    </Popover>
  );
};

function SidebarProductDetailstable({
  showVersionDetails = {},
  productDetail = [],
  // customColumns = [],
  compareTwoObj = () => {},
  historyData = {},
  type,
  showtable = false,
  productObjectId,
  fromKanban,
}) {
  let headerFor;
  if (type === 'purchaseOrder') {
    headerFor = 'activePOProductDetailsHeader';
  }
  const { data: headerInfo } = useGetHeaderByPageQuery({
    headerFor,
  });

  const [activeHeader, _setActiveHeader] = useState(
    headerInfo?.headers || DEFAULT_PO_PRODUCT_DETAILS_HEADER
  );

  let realTimeUser = JSON.parse(localStorage.getItem('user'))?.user;

  const { data: allProducts } = useGetAllProductsForOptionsQuery();

  let columnKeys = QUOTATION_FIELDS?.map((elem) => elem?.value);
  let columnKeysSalesOrder = SALES_ORDER_FIELDS?.map((elem) => elem?.value);
  const currentVersion = showVersionDetails?.version;
  const comparableVersion = currentVersion - 1;

  let diffToShow = {};

  if (comparableVersion !== 0) {
    diffToShow = compareTwoObj(
      historyData?.versionData?.[comparableVersion - 1],
      historyData?.versionData?.[currentVersion - 1]
    );
  }
  const getAddedChanges = (key) => {
    return diffToShow?.added?.data?.[key];
  };

  const getUpdatedChanges = (key) => {
    if (diffToShow?.updated?.data?.[key]) {
      return diffToShow?.updated?.data?.[key];
    }
    return {};
  };

  const calculateTaxAmount = (amount, percentage) =>
    (amount * (percentage || 0)) / 100;

  const calculateTotalAmount = (item) => {
    const sgst = calculateTaxAmount(item?.amount, item?.sgst);
    const cgst = calculateTaxAmount(item?.amount, item?.cgst);
    const igst = calculateTaxAmount(item?.amount, item?.igst);
    const totalAmount = item?.amount + sgst + cgst + igst;
    return totalAmount;
  };

  const getCompleteProduct = (id) => {
    return allProducts?.find((product) => product?.value === id);
  };

  const getConversionValue = (conversion) => {
    return (
      conversion && (
        <span
          style={{
            whiteSpace: 'nowrap',
          }}
          className=" text-[10px]  !text-gray-500"
        >
          {'( =' +
            conversion.conversionValue +
            ' ' +
            conversion?.conversionUnit +
            ')'}
        </span>
      )
    );
  };

  return (
    <>
      <div className="w-full overflow-x-scroll ">
        {type === 'quotation' && (
          <section className="mt-8" style={{ fontSize: '15px' }}>
            <p className="text-lg text-gray-600">Product Details</p>
            <div className="w-full  mt-4 !border !rounded-xl ">
              <table className="w-full min-w-full divide-y divide-gray-200 ">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="text-center px-4 py-2 text-[10px] font-medium text-gray-500 uppercase tracking-wider">
                      Product Name
                    </th>
                    <th className="text-center px-4 py-2 text-[10px] font-medium text-gray-500 uppercase tracking-wider">
                      HSN/SAC
                    </th>
                    <th className="text-center px-4 py-2 text-[10px] font-medium text-gray-500 uppercase tracking-wider">
                      UOM
                    </th>
                    <th className="text-center px-4 py-2 text-[10px] font-medium text-gray-500 uppercase tracking-wider">
                      Quantity
                    </th>
                    <th className="text-center px-4 py-2 text-[10px] font-medium text-gray-500 uppercase tracking-wider">
                      Rate
                    </th>
                    <th className="text-center px-4 py-2 text-[10px] font-medium text-gray-500 uppercase tracking-wider">
                      Discount
                    </th>
                    <th className="text-center px-4 py-2 text-[10px] font-medium text-gray-500 uppercase tracking-wider">
                      Amount
                    </th>
                    {!showVersionDetails?.data?.showIgst ? (
                      <>
                        <th className="text-center px-4 py-2 text-[10px] font-medium text-gray-500 uppercase tracking-wider">
                          CGST%
                        </th>
                        <th className="text-center px-4 py-2 text-[10px] font-medium text-gray-500 uppercase tracking-wider">
                          SGST%
                        </th>
                      </>
                    ) : (
                      <th className="text-center px-4 py-2 text-[10px] font-medium text-gray-500 uppercase tracking-wider">
                        IGST%
                      </th>
                    )}
                    <th className="text-center px-4 py-2 text-[10px] font-medium text-gray-500 uppercase tracking-wider">
                      Total Amount
                    </th>
                    {Object.keys(productDetail?.[0]?.customColumns || {})?.map(
                      (col, idx) => (
                        <th
                          className="text-center px-4 py-2 text-[10px] font-medium text-gray-500 uppercase tracking-wider"
                          key={idx}
                        >
                          {col}
                        </th>
                      )
                    )}
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {productDetail?.map((product, index) => {
                    const productDetails =
                      getUpdatedChanges('productDetails')?.[index] || {};
                    let productAdd =
                      getAddedChanges('productDetails')?.[index] || {};
                    const completeProduct = allProducts?.find(
                      (pro) => pro?.name === product.productName
                    );
                    const conversion = getCorrespondingConversionFactor(
                      product?.uom,
                      completeProduct
                    );
                    if (productAdd?.namesList && !productAdd?.productName) {
                      productAdd = {};
                    }
                    const {
                      productName,
                      uom,
                      rate,
                      quantity,
                      HsnSacCode,
                      discount,
                      amount,
                      cgst,
                      sgst,
                      igst,
                      totalAmount,
                    } = productDetails;
                    return (
                      <tr
                        key={index}
                        className={`${Object.keys(productAdd).length > 0 && showtable && 'bg-red-100'}`}
                      >
                        <td className="px-4 py-2 text-[10px]">
                          <div className="">
                            <div className="flex items-center gap-2">
                              {product?.attachments?.[0]?.data && (
                                <Popover
                                  content={
                                    <img
                                      src={product.attachments[0].data}
                                      alt="Product"
                                      style={{
                                        maxWidth: '300px',
                                        maxHeight: '300px',
                                      }}
                                    />
                                  }
                                  trigger="hover"
                                  className="z-[10000]"
                                >
                                  <img
                                    src={product.attachments[0].data}
                                    alt="Product"
                                    className="mr-2 cursor-pointer"
                                    style={{
                                      width: '30px',
                                      height: '30px',
                                      objectFit: 'cover',
                                    }}
                                  />
                                </Popover>
                              )}
                              <div
                                className={`!max-w-[30ch] !min-w-[24rem] break-words ${
                                  productName && showtable
                                    ? 'text-red-500 font-semibold'
                                    : ''
                                }`}
                                dangerouslySetInnerHTML={{
                                  __html: getDecodedHTML(
                                    product?.productName ||
                                      showVersionDetails?.items?.[index]?.item
                                        ?.name ||
                                      '-'
                                  ),
                                }}
                              />
                            </div>
                            <p className="max-w-[200px] truncate text-xs text-gray-500">
                              {product?.remarks ? `(${product?.remarks})` : ''}
                            </p>
                          </div>
                        </td>
                        <td
                          className={`text-center px-4 py-2 max-w-[10ch]  break-words text-[10px] ${HsnSacCode && showtable && 'text-red-500 font-semibold'}`}
                        >
                          {product?.HsnSacCode ||
                            showVersionDetails?.items?.[index]?.item
                              ?.hsn_sacCode ||
                            '-'}
                        </td>
                        <td
                          className={`text-center px-4 py-2 max-w-[16ch]  break-words text-[10px] ${uom && showtable && 'text-red-500 font-semibold'}`}
                        >
                          {product?.uom || '-'}
                          {getConversionValue(conversion)}
                        </td>
                        <td
                          className={`text-center px-4 py-2 max-w-[10ch]  break-words text-[10px] ${quantity && showtable && 'text-red-500 font-semibold'}`}
                        >
                          {product?.quantity || 0}
                        </td>
                        {!realTimeUser?.columnAccess?.includes(
                          columnKeys?.[12]
                        ) ? (
                          <td
                            className={`text-center px-4 py-2 max-w-[10ch]  break-words text-[10px] ${rate && showtable && 'text-red-500 font-semibold'}`}
                          >
                            {product?.rate || 0}
                          </td>
                        ) : (
                          <td>
                            <div className="h-[3rem] w-full bg-slate-200 flex items-center justify-center">
                              <FaLock className="text-2xl text-slate-400" />
                            </div>
                          </td>
                        )}
                        {!realTimeUser?.columnAccess?.includes(
                          columnKeys?.[12]
                        ) ? (
                          <td
                            className={`text-center px-4 py-2 max-w-[10ch]  break-words text-[10px] ${discount && showtable && 'text-red-500 font-semibold'}`}
                          >
                            {product?.discount || 0} %
                          </td>
                        ) : (
                          <td>
                            <div className="h-[3rem] w-full bg-slate-200 flex items-center justify-center">
                              <FaLock className="text-2xl text-slate-400" />
                            </div>
                          </td>
                        )}
                        {!realTimeUser?.columnAccess?.includes(
                          columnKeys?.[12]
                        ) ? (
                          <td
                            className={`text-center px-4 py-2 max-w-[10ch]  break-words text-[10px] ${amount && showtable && 'text-red-500 font-semibold'}`}
                          >
                            {addCommaToAmount(product?.amount || 0)}
                          </td>
                        ) : (
                          <td>
                            <div className="h-[3rem] w-full bg-slate-200 flex items-center justify-center">
                              <FaLock className="text-2xl text-slate-400" />
                            </div>
                          </td>
                        )}
                        {!showVersionDetails?.data?.showIgst ? (
                          <>
                            {' '}
                            {!realTimeUser?.columnAccess?.includes(
                              columnKeys?.[12]
                            ) ? (
                              <td
                                className={`text-center px-4 py-2 max-w-[10ch]  break-words text-[10px] ${cgst && showtable && 'text-red-500 font-semibold'}`}
                              >
                                {product?.cgst || 0}%
                              </td>
                            ) : (
                              <td>
                                <div className="h-[3rem] w-full bg-slate-200 flex items-center justify-center">
                                  <FaLock className="text-2xl text-slate-400" />
                                </div>
                              </td>
                            )}
                            {!realTimeUser?.columnAccess?.includes(
                              columnKeys?.[12]
                            ) ? (
                              <td
                                className={`text-center px-4 py-2 max-w-[10ch]  break-words text-[10px] ${sgst && showtable && 'text-red-500 font-semibold'}`}
                              >
                                {product?.sgst || 0}%
                              </td>
                            ) : (
                              <td>
                                <div className="h-[3rem] w-full bg-slate-200 flex items-center justify-center">
                                  <FaLock className="text-2xl text-slate-400" />
                                </div>
                              </td>
                            )}
                          </>
                        ) : (
                          <>
                            {!realTimeUser?.columnAccess?.includes(
                              columnKeys?.[12]
                            ) ? (
                              <td
                                className={`text-center px-4 py-2 max-w-[10ch]  break-words text-[10px] ${igst && showtable && 'text-red-500 font-semibold'}`}
                              >
                                {product?.igst || 0}%
                              </td>
                            ) : (
                              <td>
                                <div className="h-[3rem] w-full bg-slate-200 flex items-center justify-center">
                                  <FaLock className="text-2xl text-slate-400" />
                                </div>
                              </td>
                            )}
                          </>
                        )}
                        {!realTimeUser?.columnAccess?.includes(
                          columnKeys?.[12]
                        ) ? (
                          <td
                            className={`text-center px-4 py-2 max-w-[10ch]  break-words text-[10px] ${totalAmount && showtable && 'text-red-500 font-semibold'}`}
                          >
                            {addCommaToAmount(product?.totalAmount || 0)}
                          </td>
                        ) : (
                          <td>
                            <div className="h-[3rem] w-full bg-slate-200 flex items-center justify-center">
                              <FaLock className="text-2xl text-slate-400" />
                            </div>
                          </td>
                        )}
                        {Object.values(product?.customColumns || {})?.map(
                          (col, idx) => {
                            const yes = getUpdatedChanges(
                              'productDetails.customColumns'?.[col]
                            );
                            return (
                              <td
                                className={`text-center px-4 py-2 max-w-[10ch] text-[10px]  break-words ${yes && showtable && 'text-red-500 font-semibold'}`}
                                key={idx}
                              >
                                {col}
                              </td>
                            );
                          }
                        )}
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </section>
        )}
        {type === 'purchaseOrder' && (
          <section className="mt-8">
            <Title level={5} className="text-gray-600">
              Product Details
            </Title>
            <div className="w-full  mt-4 !border overflow-y-hidden !rounded-xl ">
              <table className="w-full min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  {activeHeader?.map((header, index) => {
                    if (header.key === 'item details') {
                      return (
                        <th
                          className="max-w-[10ch] text-center px-4 py-2 text-[10px] font-medium text-gray-500 uppercase tracking-wider"
                          key={index}
                        >
                          {header?.headerName}
                        </th>
                      );
                    }

                    // Conditional display for CGST and SGST, or IGST

                    if (header?.key === 'cgst') {
                      return (
                        !showVersionDetails?.showIgst && (
                          <>
                            <th className="text-center px-4 py-2 text-[10px] text-gray-500 uppercase tracking-wider font-medium">
                              {header?.headerName}
                            </th>
                          </>
                        )
                      );
                    }

                    if (header?.key === 'sgst') {
                      return (
                        !showVersionDetails?.showIgst && (
                          <>
                            <th className="text-center px-4 py-2 text-[10px] text-gray-500 uppercase tracking-wider font-medium">
                              {header?.headerName}
                            </th>
                          </>
                        )
                      );
                    }

                    if (header?.key === 'igst') {
                      return (
                        showVersionDetails?.showIgst && (
                          <>
                            <th className="text-center px-4 py-2 text-[10px] text-gray-500 uppercase tracking-wider font-medium">
                              {header?.headerName}
                            </th>
                          </>
                        )
                      );
                    }

                    // Render default column headers

                    return (
                      <th
                        className="text-center px-4 py-2 text-[10px] text-gray-500 uppercase font-medium"
                        key={index}
                      >
                        {header?.headerName}
                      </th>
                    );
                  })}
                </thead>

                <tbody className="bg-white divide-y divide-gray-200">
                  {showVersionDetails?.data?.items?.map((item, index) => {
                    const conversion = getCorrespondingConversionFactor(
                      item?.uom || item.item.uom,
                      item?.item
                    );
                    const productDetails =
                      getUpdatedChanges('items')?.[index] || {};
                    let productAdd = getAddedChanges('items')?.[index] || {};
                    if (!productAdd?.item?.name) {
                      productAdd = {};
                    }
                    const {
                      item: itemName,
                      uom,
                      rate,
                      quantity,
                      hsn_sacCode,
                      discount,

                      cgst,
                      sgst,
                      igst,
                    } = productDetails || {};

                    return (
                      <tr
                        key={index}
                        className={`${Object.keys(productAdd)?.length > 0 && showtable && 'bg-red-100'}`}
                      >
                        {activeHeader?.map((el) => {
                          switch (el?.key) {
                            case 'item details':
                              return (
                                <td
                                  className={`text-center px-4 max-w-[10ch] break-words text-[10px] py-2 ${itemName?.name && showtable && 'text-red-500 font-semibold'}`}
                                >
                                  {item.item.name ? (
                                    <>
                                      {item.item.name.length > 10 ? (
                                        <>
                                          <span
                                            data-tooltip-id={`item-tooltip-${index}`}
                                            data-tooltip-content={
                                              item.item.name
                                            }
                                            className="cursor-pointer truncate"
                                          >
                                            {`${item.item.name.slice(0, 10)}...`}
                                          </span>
                                          <ReactTooltip
                                            id={`item-tooltip-${index}`}
                                            className="max-w-[20ch] break-words"
                                          />
                                        </>
                                      ) : (
                                        <a href={`product/${item.item.name}`}>
                                          {item.item.name}
                                        </a>
                                      )}
                                    </>
                                  ) : (
                                    '-'
                                  )}
                                </td>
                              );

                            case 'hsn/sac code':
                              return (
                                <td
                                  className={`text-center px-4 max-w-[10ch]  break-words text-[10px] py-2 ${hsn_sacCode && showtable && 'text-red-500 font-semibold'}`}
                                >
                                  {item?.hsn_sacCode || item?.hsn || '-'}
                                </td>
                              );

                            case 'quantity':
                              return (
                                <>
                                  <td
                                    className={`text-center px-4 max-w-[10ch]  break-words text-[10px] py-2 ${quantity && showtable && 'text-red-500 font-semibold'}`}
                                  >
                                    {item?.quantity || 0}
                                  </td>
                                </>
                              );
                            case 'uom':
                              return (
                                <>
                                  <div className="flex flex-col items-center">
                                    <td
                                      className={`text-center px-4 max-w-[25ch]  break-words text-[10px] py-2 ${uom && showtable && 'text-red-500 font-semibold'}`}
                                    >
                                      {item?.uom || item?.item?.uom || '-'}
                                    </td>
                                    {getConversionValue(conversion)}
                                  </div>
                                </>
                              );
                            case 'rate':
                              return (
                                <>
                                  <td
                                    className={`text-center px-4 max-w-[10ch]  break-words text-[10px] py-2 ${rate && showtable && 'text-red-500 font-semibold'}`}
                                  >
                                    {item?.rate
                                      ? `${item.rate.toLocaleString('en-IN', {
                                          minimumFractionDigits: 2,
                                          maximumFractionDigits: 2,
                                        })}`
                                      : 0}
                                  </td>
                                </>
                              );
                            case 'discount':
                              return (
                                <>
                                  <td
                                    className={`text-center px-4 max-w-[10ch]  break-words text-[10px] py-2 ${discount && showtable && 'text-red-500 font-semibold'}`}
                                  >
                                    {item?.discount || item?.discount === 0
                                      ? `${item.discount}%`
                                      : 0}
                                  </td>
                                </>
                              );

                            case 'amount':
                              return (
                                <>
                                  <td
                                    className={`text-center px-4 max-w-[10ch]  break-words text-[10px] py-2 ${(quantity || rate || discount) && showtable && 'text-red-500 font-semibold'}`}
                                  >
                                    {addCommaToAmount(
                                      (
                                        item?.quantity *
                                        item?.rate *
                                        (1 - (item?.discount || 0) / 100)
                                      ).toFixed(2)
                                    ) || 0}
                                  </td>
                                </>
                              );

                            case 'cgst': {
                              return (
                                <>
                                  {!showVersionDetails?.showIgst ? (
                                    <td
                                      className={`text-center px-4 max-w-[10ch] break-words text-[10px] py-2 ${
                                        cgst &&
                                        showtable &&
                                        'text-red-500 font-semibold'
                                      }`}
                                    >
                                      {item?.cgst ? `${item?.cgst}%` : '0%'}
                                    </td>
                                  ) : (
                                    <td className="text-center px-4 max-w-[10ch] break-words text-[10px] py-2">
                                      0%
                                    </td>
                                  )}
                                </>
                              );
                            }

                            case 'sgst': {
                              return (
                                <>
                                  {!showVersionDetails?.showIgst ? (
                                    <td
                                      className={`text-center px-4 max-w-[10ch] break-words text-[10px] py-2 ${
                                        sgst &&
                                        showtable &&
                                        'text-red-500 font-semibold'
                                      }`}
                                    >
                                      {item?.sgst ? `${item?.sgst}%` : '0%'}
                                    </td>
                                  ) : (
                                    <td className="text-center px-4 max-w-[10ch] break-words text-[10px] py-2">
                                      0%
                                    </td>
                                  )}
                                </>
                              );
                            }

                            case 'igst': {
                              return (
                                <>
                                  {showVersionDetails?.showIgst && (
                                    <td
                                      className={`text-center px-4 max-w-[10ch] break-words text-[10px] py-2 ${
                                        igst &&
                                        showtable &&
                                        'text-red-500 font-semibold'
                                      }`}
                                    >
                                      {item?.igst || item?.igst === 0
                                        ? `${item?.igst}%`
                                        : '0%'}
                                    </td>
                                  )}
                                </>
                              );
                            }

                            case 'total amount':
                              return (
                                <>
                                  <td
                                    className={`text-center px-4 max-w-[10ch]  break-words text-[10px] py-2 ${(quantity || rate || discount) && showtable && 'text-red-500 font-semibold'}`}
                                  >
                                    {addCommaToAmount(
                                      (
                                        item.quantity *
                                        item.rate *
                                        (1 - (item.discount || 0) / 100) *
                                        (1 +
                                          ((item.cgst || 0) +
                                            (item.sgst || 0) +
                                            (item.igst || 0)) /
                                            100)
                                      ).toFixed(2)
                                    ) || 0}
                                  </td>
                                </>
                              );

                            default:
                              return (
                                <>
                                  {item?.item?.customColumns &&
                                    Object.keys(item?.item?.customColumns)
                                      .length > 0 &&
                                    Object.entries(
                                      item?.item?.customColumns
                                    ).map(([name, value], idx) => {
                                      // Flag to track if value has been rendered
                                      let rendered = false;

                                      if (
                                        el?.key?.toLowerCase() ===
                                          name?.toLowerCase() &&
                                        !rendered
                                      ) {
                                        rendered = true; // Set the flag to prevent duplicate rendering
                                        return (
                                          <td key={`custom-column-${idx}`}>
                                            {String(value).length > 10 ? (
                                              <>
                                                <span
                                                  data-tooltip-id={`tooltip-${idx}`}
                                                  data-tooltip-content={value}
                                                  data-tooltip-place="bottom"
                                                  className="hover:cursor-pointer text-center px-4 py-2 text-[10px] text-gray-500 uppercase tracking-wider font-medium truncate"
                                                >
                                                  {`${String(value).slice(0, 10)}...`}
                                                </span>
                                                <ReactTooltip
                                                  id={`tooltip-${idx}`}
                                                  className="max-w-[15ch] break-words"
                                                />
                                              </>
                                            ) : (
                                              <span className="text-center px-4 py-2 text-[10px] text-gray-500 uppercase tracking-wider font-medium truncate">
                                                {value}
                                              </span>
                                            )}
                                          </td>
                                        );
                                      }
                                      return null; // Avoid rendering if the condition is not met
                                    })}
                                </>
                              );
                          }
                        })}
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </section>
        )}

        {type === 'salesInquiry' && (
          <section className="pt-3 mt-2">
            <p className="text-sm font-medium text-gray-700">Product Details</p>
            <div className="w-full mt-2 border rounded-xl">
              <table className="w-full min-w-full divide-y divide-gray-200">
                {/* table Header */}
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-[10px] font-medium text-gray-500 uppercase tracking-wider">
                      Product Name
                    </th>
                    <th className="px-6 py-3 text-left text-[10px] font-medium text-gray-500 uppercase tracking-wider">
                      Quantity
                    </th>
                    <th className="px-6 py-3 text-left text-[10px] font-medium text-gray-500 uppercase tracking-wider">
                      Uom
                    </th>
                  </tr>
                </thead>
                {/* table Body */}
                <tbody className="bg-white divide-y divide-gray-200">
                  {productDetail?.map((product, index) => {
                    const productChange =
                      getUpdatedChanges('products')?.[index] || {};
                    const productAdd =
                      getAddedChanges('products')?.[index] || {};

                    const isNameChange = productChange?.productName
                      ? true
                      : false;
                    const isQuantityChange = productChange?.quantity
                      ? true
                      : false;
                    const isUomChange = productChange?.uom ? true : false;

                    return (
                      <tr
                        key={index}
                        className={`${Object.keys(productAdd).length > 0 && showtable ? '!bg-red-100 font-semibold' : ''}`}
                      >
                        <td className="px-6 py-4 text-[10px] max-w-[15ch] break-words">
                          <div
                            className={`max-w-[10ch] break-words text-sm text-gray-500 ${isNameChange && showtable ? 'text-red-500 font-semibold' : 'text-gray-600'}`}
                          >
                            {product?.productName || '-'}
                          </div>
                        </td>
                        <td className="px-6 py-4 text-[10px]">
                          <div
                            className={`max-w-[10ch] text-[10px] break-words ${isQuantityChange && showtable ? 'text-red-500 font-semibold' : 'text-gray-600'}`}
                          >
                            {product?.quantity || '-'}
                          </div>
                        </td>
                        <td className="px-6 py-4 text-[10px]">
                          <div
                            className={`max-w-[10ch] break-words ${isUomChange && showtable ? 'text-red-500 font-semibold' : 'text-gray-600'}`}
                          >
                            {product?.uom || '-'}
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </section>
        )}

        {type === 'salesOrder' && (
          <div className="list-none mt-6">
            <Title level={5} className="text-gray-800">
              Product Details
            </Title>
            <div className="w-full mt-4 !border !rounded-xl">
              <table className="w-full min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className=" px-4 py-2 text-[10px] font-medium text-gray-500 uppercase tracking-wider">
                      Product Name
                    </th>
                    <th className="text-center px-4 py-2 text-[10px] font-medium text-gray-500 uppercase tracking-wider">
                      HSN/SAC
                    </th>
                    <th className="text-center px-4 py-2 text-[10px] font-medium text-gray-500 uppercase tracking-wider">
                      UOM
                    </th>
                    <th className="text-center px-4 py-2 text-[10px] font-medium text-gray-500 uppercase tracking-wider">
                      Quantity
                    </th>
                    <th className="text-center px-4 py-2 text-[10px] font-medium text-gray-500 uppercase tracking-wider">
                      Rate
                    </th>
                    <th className="text-center px-4 py-2 text-[10px] font-medium text-gray-500 uppercase tracking-wider">
                      Discount%
                    </th>
                    <th className="text-center px-4 py-2 text-[10px] font-medium text-gray-500 uppercase tracking-wider">
                      Amount
                    </th>
                    {!showVersionDetails?.data?.showIgst && (
                      <>
                        <th className="text-center px-4 py-2 text-[10px] font-medium text-gray-500 uppercase tracking-wider">
                          CGST%
                        </th>
                        <th className="text-center px-4 py-2 text-[10px] font-medium text-gray-500 uppercase tracking-wider">
                          SGST%
                        </th>
                      </>
                    )}
                    {showVersionDetails?.data?.showIgst && (
                      <th className="text-center px-4 py-2 text-[10px] font-medium text-gray-500 uppercase tracking-wider">
                        IGST%
                      </th>
                    )}
                    <th className="text-center px-4 py-2 text-[10px] font-medium text-gray-500 uppercase tracking-wider">
                      Total Amount
                    </th>
                    {Object.keys(
                      showVersionDetails?.data?.products?.[0]?.customColumns ||
                        {}
                    ).map((col, idx) => (
                      <th
                        key={idx}
                        className="text-center px-4 py-2 text-[10px] font-medium text-gray-500 uppercase tracking-wider"
                      >
                        {col}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {showVersionDetails?.data.items?.map((item, n) => {
                    const currentItem = getUpdatedChanges('items')?.[n] || {};
                    let currentAddedItem = getAddedChanges('items')?.[n] || {};

                    if (
                      currentAddedItem?.namesList &&
                      !currentAddedItem.details
                    ) {
                      currentAddedItem = {};
                    }
                    const isNameChange = currentItem?.details ? true : false;
                    const isHsnChange = currentItem?.hsn ? true : false;
                    const isUomChange = currentItem?.UOM ? true : false;
                    const isQuantityChange = currentItem?.quantity
                      ? true
                      : false;
                    const isRateChange = currentItem?.rate ? true : false;
                    const isDiscountChange = currentItem?.discount
                      ? true
                      : false;
                    const isCgstChange = currentItem?.cgst ? true : false;
                    const isIgstChange = currentItem?.igst ? true : false;
                    const isSgstChange = currentItem?.sgst ? true : false;
                    const isAmountChange = currentItem?.amount ? true : false;
                    // const changesCustomColumns =
                    //   currentItem?.item?.customColumns;

                    //  Hiding Products From Kanbam
                    if (
                      fromKanban &&
                      productObjectId.toString() === item?._id?.toString()
                    )
                      return null;
                    return (
                      <tr
                        key={n}
                        className={`${Object.keys(currentAddedItem).length > 0 && showtable && 'bg-red-100'}`}
                      >
                        <td className=" px-4 py-2 text-[10px]">
                          <div
                            className={`${isNameChange && showtable && 'text-red-500 font-semibold'} max-w-[50ch]
                          !min-w-[24rem]  break-words`}
                          >
                            {item?.details}
                            <p className="max-w-[200px] truncate text-xs text-gray-500">
                              {item?.remarks ? `(${item?.remarks})` : ''}
                            </p>
                          </div>
                        </td>
                        <td
                          className={`text-center px-4 py-2 text-[10px] ${isHsnChange && showtable && 'text-red-500 font-semibold'}`}
                        >
                          {item?.hsn}
                        </td>
                        <td
                          className={`text-center px-4 py-2 text-[10px] ${isUomChange && showtable && 'text-red-500 font-semibold'}`}
                        >
                          {item?.UOM}
                        </td>
                        <td
                          className={`text-center px-4 py-2 text-[10px] ${isQuantityChange && showtable && 'text-red-500 font-semibold'}`}
                        >
                          {item?.quantity}
                        </td>
                        {realTimeUser?.columnAccess?.includes(
                          columnKeysSalesOrder?.[7]
                        ) ? (
                          <td
                            className={`text-center px-4 py-2 text-[10px] ${isRateChange && showtable && 'text-red-500 font-semibold'}`}
                          >
                            {item?.rate}
                          </td>
                        ) : (
                          <td>
                            <div className="h-[3rem] w-full bg-slate-200 flex items-center justify-center">
                              <FaLock className="text-2xl text-slate-400" />
                            </div>
                          </td>
                        )}
                        {!realTimeUser?.columnAccess?.includes(
                          columnKeysSalesOrder?.[7]
                        ) ? (
                          <td
                            className={`text-center px-4 py-2 text-[10px] ${isDiscountChange && showtable && 'text-red-500 font-semibold'}`}
                          >
                            {item?.discount} %
                          </td>
                        ) : (
                          <td>
                            <div className="h-[3rem] w-full bg-slate-200 flex items-center justify-center">
                              <FaLock className="text-2xl text-slate-400" />
                            </div>
                          </td>
                        )}
                        <td
                          className={`text-center px-4 py-2 text-[10px] ${isAmountChange && showtable && 'text-red-500 font-semibold'}`}
                        >
                          {addCommaToAmount(item?.amount)}
                        </td>
                        {!showVersionDetails?.data?.showIgst ? (
                          <>
                            <td
                              className={`text-center px-4 py-2 text-[10px] ${isCgstChange && showtable && 'text-red-500 font-semibold'}`}
                            >
                              {item?.cgst}%
                            </td>
                            <td
                              className={`text-center px-4 py-2 text-[10px] ${isSgstChange && showtable && 'text-red-500 font-semibold'}`}
                            >
                              {item?.sgst}%
                            </td>
                          </>
                        ) : (
                          <td
                            className={`text-center px-4 py-2 text-[10px] ${isIgstChange && showtable && 'text-red-500 font-semibold'}`}
                          >
                            {item?.igst}%
                          </td>
                        )}

                        <td
                          className={`text-center px-4 py-2 text-[10px] ${(isSgstChange || isCgstChange || isIgstChange || isAmountChange) && showtable && 'text-red-500 font-semibold'}`}
                        >
                          {addCommaToAmount(
                            calculateTotalAmount(item)?.toFixed(2)
                          )}
                        </td>
                        {Object.values(item?.customColumns || {})?.map(
                          (col, idx) => (
                            <td
                              className="text-center px-4 py-2 max-w-[10ch] text-[10px]  break-words"
                              key={idx}
                            >
                              {col}
                            </td>
                          )
                        )}
                      </tr>
                    );
                  })}
                  {showVersionDetails?.data.products?.map((item, n) => {
                    const currentItem =
                      getUpdatedChanges('products')?.[n] || {};
                    let currentAddedItem =
                      getAddedChanges('products')?.[n] || {};
                    const completeProduct = getCompleteProduct(item?.value);
                    const conversion = getCorrespondingConversionFactor(
                      item?.UOM,
                      completeProduct
                    );
                    if (!currentAddedItem.details) {
                      currentAddedItem = {};
                    }
                    const isNameChange = currentItem?.details ? true : false;
                    const isHsnChange = currentItem?.hsn ? true : false;
                    const isUomChange = currentItem?.UOM ? true : false;
                    const isQuantityChange = currentItem?.quantity
                      ? true
                      : false;
                    const isRateChange = currentItem?.rate ? true : false;
                    const isDiscountChange = currentItem?.discount
                      ? true
                      : false;
                    const isCgstChange = currentItem?.cgst ? true : false;
                    const isIgstChange = currentItem?.igst ? true : false;
                    const isSgstChange = currentItem?.sgst ? true : false;
                    const isAmountChange = currentItem?.amount ? true : false;

                    //  HighLighting Products From Kanba
                    let highLight = false;
                    if (
                      fromKanban &&
                      productObjectId &&
                      productObjectId.toString() === item?._id?.toString()
                    ) {
                      highLight = true;
                    }
                    return (
                      <tr
                        key={n}
                        className={`${Object.keys(currentAddedItem).length > 0 && showtable && 'bg-red-100'} ${highLight && 'bg-green-50'}`}
                      >
                        <td className=" px-4 py-2 text-[10px] flex gap-1 items-center">
                          {item?.attachments?.[0] && (
                            <ShowMedia
                              id={
                                item?.attachments?.[0]?._id ||
                                item.attachments?.[0] ||
                                ''
                              }
                            />
                          )}
                          <div
                            className={`${isNameChange && showtable && 'text-red-500 font-semibold'} max-w-[50ch] !min-w-[24rem] break-words`}
                            dangerouslySetInnerHTML={{
                              __html: `${getDecodedHTML(item?.details)}<p>${item?.remarks ? `(${item?.remarks})` : ''}</p>`,
                            }}
                          ></div>
                        </td>
                        <td
                          className={`text-center px-4 py-2 text-[10px] ${isHsnChange && showtable && 'text-red-500 font-semibold'}`}
                        >
                          {item?.hsn}
                        </td>
                        <td
                          className={`text-center px-4 py-2 text-[10px] ${isUomChange && showtable && 'text-red-500 font-semibold'}`}
                        >
                          {item?.UOM}
                          {getConversionValue(conversion)}
                        </td>
                        <td
                          className={`text-center px-4 py-2 text-[10px] ${isQuantityChange && showtable && 'text-red-500 font-semibold'}`}
                        >
                          {item?.quantity}
                        </td>
                        {!realTimeUser?.columnAccess?.includes(
                          columnKeysSalesOrder?.[7]
                        ) ? (
                          <td
                            className={`text-center px-4 py-2 text-[10px] ${isRateChange && showtable && 'text-red-500 font-semibold'}`}
                          >
                            {item?.rate}
                          </td>
                        ) : (
                          <td>
                            <div className="h-[3rem] w-full bg-slate-200 flex items-center justify-center">
                              <FaLock className="text-2xl text-slate-400" />
                            </div>
                          </td>
                        )}
                        {!realTimeUser?.columnAccess?.includes(
                          columnKeysSalesOrder?.[7]
                        ) ? (
                          <td
                            className={`text-center px-4 py-2 text-[10px] ${isDiscountChange && showtable && 'text-red-500 font-semibold'}`}
                          >
                            {item?.discount}
                          </td>
                        ) : (
                          <td>
                            <div className="h-[3rem] w-full bg-slate-200 flex items-center justify-center">
                              <FaLock className="text-2xl text-slate-400" />
                            </div>
                          </td>
                        )}
                        {!realTimeUser?.columnAccess?.includes(
                          columnKeysSalesOrder?.[7]
                        ) ? (
                          <td
                            className={`text-center px-4 py-2 text-[10px] ${isAmountChange && showtable && 'text-red-500 font-semibold'}`}
                          >
                            {addCommaToAmount(item?.amount)}
                          </td>
                        ) : (
                          <td>
                            <div className="h-[3rem] w-full bg-slate-200 flex items-center justify-center">
                              <FaLock className="text-2xl text-slate-400" />
                            </div>
                          </td>
                        )}
                        {!showVersionDetails?.data?.showIgst ? (
                          <>
                            {' '}
                            {!realTimeUser?.columnAccess?.includes(
                              columnKeysSalesOrder?.[7]
                            ) ? (
                              <td
                                className={`text-center px-4 py-2 text-[10px] ${isCgstChange && showtable && 'text-red-500 font-semibold'}`}
                              >
                                {item?.cgst}%
                              </td>
                            ) : (
                              <td>
                                <div className="h-[3rem] w-full bg-slate-200 flex items-center justify-center">
                                  <FaLock className="text-2xl text-slate-400" />
                                </div>
                              </td>
                            )}
                            {!realTimeUser?.columnAccess?.includes(
                              columnKeysSalesOrder?.[7]
                            ) ? (
                              <td
                                className={`text-center px-4 py-2 text-[10px] ${isSgstChange && showtable && 'text-red-500 font-semibold'}`}
                              >
                                {item?.sgst}%
                              </td>
                            ) : (
                              <td>
                                <div className="h-[3rem] w-full bg-slate-200 flex items-center justify-center">
                                  <FaLock className="text-2xl text-slate-400" />
                                </div>
                              </td>
                            )}
                          </>
                        ) : (
                          <>
                            {!realTimeUser?.columnAccess?.includes(
                              columnKeysSalesOrder?.[7]
                            ) ? (
                              <td
                                className={`text-center px-4 py-2 text-[10px] ${isIgstChange && showtable && 'text-red-500 font-semibold'}`}
                              >
                                {item?.igst}%
                              </td>
                            ) : (
                              <td>
                                <div className="h-[3rem] w-full bg-slate-200 flex items-center justify-center">
                                  <FaLock className="text-2xl text-slate-400" />
                                </div>
                              </td>
                            )}
                          </>
                        )}
                        {!realTimeUser?.columnAccess?.includes(
                          columnKeysSalesOrder?.[7]
                        ) ? (
                          <td
                            className={`text-center px-4 py-2 text-[10px] ${(isSgstChange || isCgstChange || isIgstChange || isAmountChange) && showtable && 'text-red-500 font-semibold'}`}
                          >
                            {addCommaToAmount(
                              calculateTotalAmount(item)?.toFixed(2)
                            )}
                          </td>
                        ) : (
                          <td>
                            <div className="h-[3rem] w-full bg-slate-200 flex items-center justify-center">
                              <FaLock className="text-2xl text-slate-400" />
                            </div>
                          </td>
                        )}
                        {Object.keys(
                          showVersionDetails?.data?.products?.[0]
                            ?.customColumns || {}
                        ).map((col, idx) => (
                          <td
                            key={idx}
                            className="text-center px-4 py-2 text-[10px]"
                          >
                            {item?.customColumns?.[col] || '-'}
                          </td>
                        ))}
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>
    </>
  );
}

export default SidebarProductDetailstable;
