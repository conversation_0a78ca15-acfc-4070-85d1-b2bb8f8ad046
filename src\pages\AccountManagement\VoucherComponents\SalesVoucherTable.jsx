import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import {
  <PERSON>ton,
  Popconfirm,
  Space,
  Table,
  Tag,
  Tooltip,
  Typography,
} from 'antd';
import { useMediaQuery } from 'react-responsive';
import { toast } from 'react-toastify';
import { mobileWidth } from '../../../helperFunction';
import { useDeleteVoucherMutation } from '../../../slices/AccountManagement/voucherApiSlice';

const { Text } = Typography;

const SalesVoucherTable = ({
  rows,
  isLoading,
  checkedRows,
  handleSelectAll,
  selectAll,
  handleCheckBoxChange,
  setOpenSidebar,
  setVoucherId,
  onEdit,
}) => {
  const [deleteVoucher] = useDeleteVoucherMutation();
  const isMobile = useMediaQuery({ query: mobileWidth });

  const handleDelete = async (record) => {
    try {
      const res = await deleteVoucher({ data: { id: record._id } });
      if (res.error) {
        toast.error('Failed to delete voucher. Please reload and try again.');
      } else {
        toast.success('Voucher deleted successfully');
      }
    } catch (error) {
      toast.error('An error occurred while deleting the voucher.');
    }
  };

  const calculateItemsTotal = (items) => {
    if (!items || items.length === 0) return 0;
    return items.reduce((total, item) => total + (parseFloat(item?.totalAmount || item?.amount || 0)), 0);
  };

  const columns = [
    !isMobile && {
      title: (
        <div>
          {rows?.length > 0 && (
            <label>
              <input
                type="checkbox"
                checked={selectAll}
                onChange={handleSelectAll}
                style={{ marginRight: 8 }}
              />
              Select All ({checkedRows?.length || 0})
            </label>
          )}
        </div>
      ),
      dataIndex: 'selection',
      key: 'selection',
      width: 120,
      render: (_, record) => (
        <input
          type="checkbox"
          checked={checkedRows?.includes(record)}
          onChange={(e) => {
            handleCheckBoxChange(e, record);
            e.stopPropagation();
          }}
        />
      ),
    },
    {
      title: 'Sales Voucher ID',
      dataIndex: 'salesVoucherId',
      key: 'salesVoucherId',
      width: 180,
      render: (text, record) => (
        <Button
          type="link"
          onClick={() => {
            setVoucherId(record._id);
            setOpenSidebar(true);
          }}
          style={{ padding: 0, height: 'auto' }}
        >
          <Text strong style={{ color: '#1890ff' }}>
            {text || '-'}
          </Text>
        </Button>
      ),
    },
    {
      title: 'Vendor/Customer',
      key: 'vendorCustomer',
      width: 200,
      render: (_, record) => {
        const vendor = record?.salesVoucherData?.vendor || record?.vendor;
        const customer = record?.salesVoucherData?.customer || record?.customer;
        
        if (vendor) {
          return (
            <div
              onClick={() => {
                setVoucherId(record._id);
                setOpenSidebar(true);
              }}
              className="cursor-pointer"
            >
              <Text>{vendor?.name}</Text>
              <br />
              <Tag size="small" color="blue">
                Vendor
              </Tag>
            </div>
          );
        }
        if (customer) {
          return (
            <div
              onClick={() => {
                setVoucherId(record._id);
                setOpenSidebar(true);
              }}
              className="cursor-pointer"
            >
              <Text>{customer?.name}</Text>
              <br />
              <Tag size="small" color="green">
                Customer
              </Tag>
            </div>
          );
        }
        return '-';
      },
    },
    {
      title: 'Ledger Type',
      key: 'ledgerType',
      width: 150,
      render: (_, record) => (
        <Tag color="purple">
          {record?.salesVoucherData?.ledgerType?.name || record?.ledgerType?.name || '-'}
        </Tag>
      ),
    },
    {
      title: 'Items Count',
      key: 'itemsCount',
      width: 120,
      render: (_, record) => (
        <Tag color="green">
          {record?.salesVoucherData?.items?.length || 0} items
        </Tag>
      ),
    },
    {
      title: 'Total Amount',
      key: 'totalAmount',
      width: 150,
      render: (_, record) => {
        const total = calculateItemsTotal(record?.salesVoucherData?.items);
        return (
          <Text strong style={{ color: '#52c41a' }}>
            ₹{total.toLocaleString()}
          </Text>
        );
      },
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      width: 120,
      render: (date) => new Date(date).toLocaleDateString(),
      sorter: (a, b) => new Date(a.date) - new Date(b.date),
    },
    {
      title: 'Remarks',
      dataIndex: 'remarks',
      key: 'remarks',
      width: 200,
      render: (remark) => {
        if (!remark) return '-';
        if (remark.length > 50) {
          return (
            <Tooltip title={remark} placement="topLeft">
              <Text ellipsis style={{ maxWidth: 180, display: 'block' }}>
                {remark}
              </Text>
            </Tooltip>
          );
        }
        return remark;
      },
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="Edit Sales Voucher">
            <Button
              type="text"
              icon={<EditOutlined />}
              size="small"
              onClick={() => {
                if (onEdit) {
                  onEdit(record);
                }
              }}
              style={{ color: '#52c41a' }}
            />
          </Tooltip>
          <Popconfirm
            title="Delete Sales Voucher"
            description="Are you sure you want to delete this sales voucher?"
            onConfirm={() => handleDelete(record)}
            okText="Yes"
            cancelText="No"
            okType="danger"
          >
            <Tooltip title="Delete Sales Voucher">
              <Button
                type="text"
                icon={<DeleteOutlined />}
                size="small"
                danger
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ].filter(Boolean);

  return (
    <div className="sales-voucher-table-container">
      <Table
        columns={columns}
        dataSource={rows || []}
        rowKey="_id"
        loading={isLoading}
        pagination={false}
        scroll={{ x: 1200 }}
      />
    </div>
  );
};

export default SalesVoucherTable;
