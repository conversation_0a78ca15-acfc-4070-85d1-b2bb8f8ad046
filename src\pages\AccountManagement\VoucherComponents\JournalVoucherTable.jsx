import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import {
  <PERSON>ton,
  Popconfirm,
  Space,
  Table,
  Tag,
  Tooltip,
  Typography,
} from 'antd';
import { useMediaQuery } from 'react-responsive';
import { toast } from 'react-toastify';
import { mobileWidth } from '../../../helperFunction';
import { useDeleteVoucherMutation } from '../../../slices/AccountManagement/voucherApiSlice';

const { Text } = Typography;

const JournalVoucherTable = ({
  rows,
  isLoading,
  checkedRows,
  handleSelectAll,
  selectAll,
  handleCheckBoxChange,
  setOpenSidebar,
  setVoucherId,
  onEdit,
}) => {
  const [deleteVoucher] = useDeleteVoucherMutation();
  const isMobile = useMediaQuery({ query: mobileWidth });

  const handleDelete = async (record) => {
    try {
      const res = await deleteVoucher({ data: { id: record._id } });
      if (res.error) {
        toast.error('Failed to delete voucher. Please reload and try again.');
      } else {
        toast.success('Voucher deleted successfully');
      }
    } catch (error) {
      toast.error('An error occurred while deleting the voucher.');
    }
  };

  const calculateDebitTotal = (accounts) => {
    if (!accounts || accounts.length === 0) return 0;
    return accounts.reduce((total, account) => total + (parseFloat(account?.debit || 0)), 0);
  };

  const calculateCreditTotal = (accounts) => {
    if (!accounts || accounts.length === 0) return 0;
    return accounts.reduce((total, account) => total + (parseFloat(account?.credit || 0)), 0);
  };

  const isBalanced = (accounts) => {
    const debitTotal = calculateDebitTotal(accounts);
    const creditTotal = calculateCreditTotal(accounts);
    return Math.abs(debitTotal - creditTotal) < 0.01; // Allow for small floating point differences
  };

  const columns = [
    !isMobile && {
      title: (
        <div>
          {rows?.length > 0 && (
            <label>
              <input
                type="checkbox"
                checked={selectAll}
                onChange={handleSelectAll}
                style={{ marginRight: 8 }}
              />
              Select All ({checkedRows?.length || 0})
            </label>
          )}
        </div>
      ),
      dataIndex: 'selection',
      key: 'selection',
      width: 120,
      render: (_, record) => (
        <input
          type="checkbox"
          checked={checkedRows?.includes(record)}
          onChange={(e) => {
            handleCheckBoxChange(e, record);
            e.stopPropagation();
          }}
        />
      ),
    },
    {
      title: 'Journal Voucher ID',
      dataIndex: 'journalVoucherId',
      key: 'journalVoucherId',
      width: 180,
      render: (text, record) => (
        <Button
          type="link"
          onClick={() => {
            setVoucherId(record._id);
            setOpenSidebar(true);
          }}
          style={{ padding: 0, height: 'auto' }}
        >
          <Text strong style={{ color: '#1890ff' }}>
            {text || '-'}
          </Text>
        </Button>
      ),
    },
    {
      title: 'Journal',
      key: 'journal',
      width: 200,
      render: (_, record) => (
        <div
          onClick={() => {
            setVoucherId(record._id);
            setOpenSidebar(true);
          }}
          className="cursor-pointer"
        >
          <Text>{record?.journalVoucherData?.journal?.name || '-'}</Text>
        </div>
      ),
    },
    {
      title: 'Reference ID',
      key: 'referenceId',
      width: 150,
      render: (_, record) => (
        <Tag color="blue">
          {record?.journalVoucherData?.referenceId || '-'}
        </Tag>
      ),
    },
    {
      title: 'Accounts Count',
      key: 'accountsCount',
      width: 130,
      render: (_, record) => (
        <Tag color="green">
          {record?.journalVoucherData?.accounts?.length || 0} accounts
        </Tag>
      ),
    },
    {
      title: 'Total Debit',
      key: 'totalDebit',
      width: 130,
      render: (_, record) => {
        const total = calculateDebitTotal(record?.journalVoucherData?.accounts);
        return (
          <Text strong style={{ color: '#f5222d' }}>
            ₹{total.toLocaleString()}
          </Text>
        );
      },
    },
    {
      title: 'Total Credit',
      key: 'totalCredit',
      width: 130,
      render: (_, record) => {
        const total = calculateCreditTotal(record?.journalVoucherData?.accounts);
        return (
          <Text strong style={{ color: '#52c41a' }}>
            ₹{total.toLocaleString()}
          </Text>
        );
      },
    },
    {
      title: 'Balance Status',
      key: 'balanceStatus',
      width: 120,
      render: (_, record) => {
        const balanced = isBalanced(record?.journalVoucherData?.accounts);
        return (
          <Tag color={balanced ? 'green' : 'red'}>
            {balanced ? 'Balanced' : 'Unbalanced'}
          </Tag>
        );
      },
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      width: 120,
      render: (date) => new Date(date).toLocaleDateString(),
      sorter: (a, b) => new Date(a.date) - new Date(b.date),
    },
    {
      title: 'Remarks',
      dataIndex: 'remarks',
      key: 'remarks',
      width: 200,
      render: (remark) => {
        if (!remark) return '-';
        if (remark.length > 50) {
          return (
            <Tooltip title={remark} placement="topLeft">
              <Text ellipsis style={{ maxWidth: 180, display: 'block' }}>
                {remark}
              </Text>
            </Tooltip>
          );
        }
        return remark;
      },
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="Edit Journal Voucher">
            <Button
              type="text"
              icon={<EditOutlined />}
              size="small"
              onClick={() => {
                if (onEdit) {
                  onEdit(record);
                }
              }}
              style={{ color: '#52c41a' }}
            />
          </Tooltip>
          <Popconfirm
            title="Delete Journal Voucher"
            description="Are you sure you want to delete this journal voucher?"
            onConfirm={() => handleDelete(record)}
            okText="Yes"
            cancelText="No"
            okType="danger"
          >
            <Tooltip title="Delete Journal Voucher">
              <Button
                type="text"
                icon={<DeleteOutlined />}
                size="small"
                danger
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ].filter(Boolean);

  return (
    <div className="journal-voucher-table-container">
      <Table
        columns={columns}
        dataSource={rows || []}
        rowKey="_id"
        loading={isLoading}
        pagination={false}
        scroll={{ x: 1400 }}
      />
    </div>
  );
};

export default JournalVoucherTable;
