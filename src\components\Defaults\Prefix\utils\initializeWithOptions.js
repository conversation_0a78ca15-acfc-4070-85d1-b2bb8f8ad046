const initializeWithOptions = (idType, setOptions) => {
  if (
    idType === 'workOrderId' ||
    idType === 'assemblyBomId' ||
    idType === 'vendorId' ||
    idType === 'storeId' ||
    idType === 'partsId' ||
    idType === 'productId' ||
    idType === 'saId' ||
    idType === 'chalanId' ||
    idType === 'lotNo' ||
    idType === 'poId' ||
    idType === 'salesOrderId' ||
    idType === 'salesInquiryId' ||
    idType === 'quotationId' ||
    idType === 'customerId' ||
    idType === 'outsourceId' ||
    idType === 'leadId' ||
    idType === 'grnId' ||
    idType === 'renewalId' ||
    idType === 'dailyTaskId' ||
    idType === 'dispatchId' ||
    idType === 'quotationVersion' ||
    idType === 'assetsId'
  ) {
    setOptions([
      'String',
      'MM-YY',
      'DD-MM-YY',
      'Increment',
      'UserEntry',
      'Dropdown',
    ]);
  } else if (idType === 'jobId' || idType === 'modelName') {
    setOptions([
      'String',
      'MM-YY',
      'DD-MM-YY',
      'Increment',
      'Dropdown',
      'UserEntry',
      'WorkOrderId',
    ]);
  } else if (idType === 'inputScreen') {
    setOptions([
      'String',
      'MM-YY',
      'DD-MM-YY',
      'Increment',
      'UserEntry',
      'Dropdown',
    ]);
  } else if (idType === 'batchId') {
    setOptions([
      'String',
      'MM-YY',
      'DD-MM-YY',
      'Increment',
      'UserEntry',
      'WorkOrderId',
      'JobId',
      'InputScreen',
      'ModelName',
      'Dropdown',
    ]);
  } else if (idType === 'qr') {
    setOptions(['ModelName', 'Process', 'BatchNo', 'Dropdown']);
  } else if (idType === 'taskId') {
    setOptions(['String', 'MM-YY', 'DD-MM-YY', 'Increment', 'Dropdown']);
  } else if (idType === 'inventoryBatchNo') {
    setOptions(['String', 'MM-YY', 'DD-MM-YY', 'Increment']);
  }
};

export default initializeWithOptions;
