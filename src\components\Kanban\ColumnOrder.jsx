import update from 'immutability-helper';
import { useCallback, useContext, useEffect, useRef, useState } from 'react';
import { useDrag, useDrop } from 'react-dnd';
import { Store } from '../../store/Store';

import { useEditUserMutation } from '../../slices/userApiSlice';

import Button from '../global/components/Button';
import RightSidebar from '../global/components/RightSidebar';

const style = {
  border: '1px dashed gray',
  // padding: '0.5rem 1rem',
  // marginBottom: '.5rem',
  backgroundColor: 'white',
  cursor: 'move',
  width: '100%',
  textAlign: 'center',
};

export const Card = ({ id, text, index, moveCard }) => {
  const ref = useRef(null);
  const [{ handlerId }, drop] = useDrop({
    accept: 'CARD',
    collect(monitor) {
      return {
        handlerId: monitor.getHandlerId(),
      };
    },
    hover(item, monitor) {
      if (!ref.current) {
        return;
      }
      const dragIndex = item.index;
      const hoverIndex = index;
      // Don't replace items with themselves
      if (dragIndex === hoverIndex) {
        return;
      }
      // Determine rectangle on screen
      const hoverBoundingRect = ref.current?.getBoundingClientRect();
      // Get vertical middle
      const hoverMiddleY =
        (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
      // Determine mouse position
      const clientOffset = monitor.getClientOffset();
      // Get pixels to the top
      const hoverClientY = clientOffset.y - hoverBoundingRect.top;
      // Only perform the move when the mouse has crossed half of the items height
      // When dragging downwards, only move when the cursor is below 50%
      // When dragging upwards, only move when the cursor is above 50%
      // Dragging downwards
      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
        return;
      }
      // Dragging upwards
      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
        return;
      }
      // Time to actually perform the action
      moveCard(dragIndex, hoverIndex);
      // Note: we're mutating the monitor item here!
      // Generally it's better to avoid mutations,
      // but it's good here for the sake of performance
      // to avoid expensive index searches.
      item.index = hoverIndex;
    },
  });
  const [{ isDragging }, drag] = useDrag({
    type: 'CARD',
    item: () => {
      return { id, index };
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });
  const opacity = isDragging ? 0 : 1;
  drag(drop(ref));
  return (
    <div
      ref={ref}
      style={{ ...style, opacity }}
      className="p-2"
      data-handler-id={handlerId}
    >
      {text}
    </div>
  );
};

const ColumnOrder = ({
  openColumnViewSidebar,
  setColumnViewSidebar,
  columns,
  setColumns,
}) => {
  const { state } = useContext(Store);
  const [columnList, setColumnList] = useState(columns);
  const [editUser] = useEditUserMutation();

  const moveCard = useCallback((dragIndex, hoverIndex) => {
    setColumnList((prevCards) =>
      update(prevCards, {
        $splice: [
          [dragIndex, 1],
          [hoverIndex, 0, prevCards[dragIndex]],
        ],
      })
    );
  }, []);

  const updateQueue = async () => {
    setColumns(columnList);
    let user = JSON.parse(localStorage.getItem('user'));
    let newUser = {
      ...user,
      user: {
        ...user?.user,
        columnView: columnList,
      },
    };
    localStorage.setItem('user', JSON.stringify(newUser));
    await editUser({ id: state?.user?._id, data: { columnView: columnList } });
  };

  const renderCard = useCallback((elem, index) => {
    return (
      <div key={index} className="flex items-center gap-2 mb-2 w-full">
        <div
          style={{ cursor: 'default' }}
          className="p-2 border-2 border-solid border-slate-400 min-w-[50px] flex items-center justify-center"
        >
          {index + 1}
        </div>
        <Card
          key={elem}
          index={index}
          id={elem}
          text={elem}
          moveCard={moveCard}
        />
      </div>
    );
  }, []); //eslint-disable-line

  useEffect(() => {
    if (columns) {
      if (
        JSON.parse(localStorage.getItem('user'))?.user?.columnView?.length > 0
      ) {
        setColumnList(
          JSON.parse(localStorage.getItem('user'))?.user?.columnView
        );
      } else {
        let columnTemp = columns?.map((elem) => elem?.label);
        setColumnList(columnTemp);
      }
    }
  }, [columns]);

  return (
    <>
      {columns && (
        <RightSidebar
          openSideBar={openColumnViewSidebar}
          setOpenSideBar={setColumnViewSidebar}
        >
          <div className="items-center flex gap-2">
            <h1 className="mb-3">Columns Order</h1>
            <div className="flex flex-end ml-auto pb-4">
              <Button
                type="button"
                onClick={updateQueue}
                // isLoading={isLoadingUpdate}
              >
                Update
              </Button>
            </div>
          </div>
          <div className="h-[87%]">
            {columnList?.map((elem, i) => {
              if (i !== columns?.length - 1) {
                return renderCard(elem, i);
              } else {
                return (
                  <div className="flex flex-end flex-col" key={i}>
                    {renderCard(elem, i)}
                  </div>
                );
              }
            })}
          </div>
        </RightSidebar>
      )}
    </>
  );
};

export default ColumnOrder;
