import { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { voucherFormStrategy } from './strategy/voucherFormStrategy';

const VoucherFormPage = () => {
  const navigate = useNavigate();
  const { type, id } = useParams();
  const [editData, setEditData] = useState(id ? { _id: id } : {});

  const handleClose = () => {
    if (type) {
      navigate(`/accountmanagement/voucher?tab=${type}`);
    } else {
      navigate('/accountmanagement/voucher');
    }
  };

  return (
    <div className="p-2">
      {voucherFormStrategy(type, {
        openModal: true,
        setOpenModal: (open) => {
          if (!open) handleClose();
        },
        editData,
        setEditData,
      })}
    </div>
  );
};

export default VoucherFormPage;
