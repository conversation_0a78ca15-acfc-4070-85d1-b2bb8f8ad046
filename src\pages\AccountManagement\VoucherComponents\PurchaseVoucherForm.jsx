import { ArrowLeftOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, DatePicker, Form } from 'antd';
import dayjs from 'dayjs';
import { useContext, useEffect, useMemo, useState } from 'react';
import { useMediaQuery } from 'react-responsive';
import { toast } from 'react-toastify';
import SelectV2 from '../../../components/global/components/SelectV2';
import Textarea from '../../../components/global/components/Textarea';
import MasterDetails from '../../../components/MasterDetails';
import ProductFormatManager from '../../../components/ProductFormats/ProductFormatManager';
import StaticProductTable from '../../../components/ProductFormats/StaticProductTable';
import { mobileWidth, tabletWidth } from '../../../helperFunction';
import usePrefixIds from '../../../hooks/usePrefixIds';
import { useGetCustomTypesQuery } from '../../../slices/AccountManagement/customTypesApiSlice';
import {
  useCreateVoucherMutation,
  useEditVoucherMutation,
  useGetVoucherByIdQuery,
} from '../../../slices/AccountManagement/voucherApiSlice';
import { useGetDropdownsQuery } from '../../../slices/dropdownApiSlice';
import {
  useGetAllVendorsForOptionsQuery,
  useGetVendorByIdQuery,
} from '../../../slices/vendorApiSlice';
import { Store } from '../../../store/Store';
import CustomTypesModal from '../global/CustomTypesModal';

const PurchaseVoucherForm = ({ props }) => {
  const { setOpenModal, editData, setEditData } = props;
  const [form] = Form.useForm();
  const [editVoucher] = useEditVoucherMutation();
  const { data: ledgerTypes, isLoading: isLedgerTypesLoading } =
    useGetCustomTypesQuery({ type: 'ledgerType' });
  const { data: vendors, isLoading: isVendorLoading } =
    useGetAllVendorsForOptionsQuery();
  const { data: voucher } = useGetVoucherByIdQuery(
    { id: editData?._id },
    { skip: editData?._id === undefined }
  );
  const isEditing = !!editData?._id;
  const isMobile = useMediaQuery({ query: mobileWidth });
  const isTablet = useMediaQuery({ query: tabletWidth });
  const { defaults: { defaultParam } = {} } = useContext(Store);
  const [createVoucher] = useCreateVoucherMutation();
  const { data: dropdownsData } = useGetDropdownsQuery();

  const [items, setItems] = useState(
    defaultParam?.projectDefaults?.showProductFormatTable || isEditing
      ? []
      : [
          {
            key: Date.now() + Math.random(),
            itemId: '',
            productName: '',
            uom: '',
            hsn: '',
            quantity: '',
            rate: '',
            discount: '',
            amount: 0,
            cgst: '',
            sgst: '',
            cgstAmount: 0,
            sgstAmount: 0,
            igst: '',
            igstAmount: 0,
            totalAmount: 0,
            color: '#FFFFFF',
          },
        ]
  );
  const [charges, setCharges] = useState({});
  const [columnVisibility, setColumnVisibility] = useState({});
  const [chargesVisibility, setChargesVisibility] = useState({});
  const [displayFormat, setDisplayFormat] = useState(null);

  const { IdGenComp, idCompData } = usePrefixIds({
    idFor: 'purchaseVoucherId',
  });
  const [openCustomTypeModal, setOpenCustomTypeModal] = useState(false);
  const [vendorLoading, setVendorLoading] = useState(false);
  const [vendor, setVendor] = useState(false);

  const { data: foundVendor } = useGetVendorByIdQuery(
    form.getFieldValue('vendor'),
    { skip: !form.getFieldValue('vendor') }
  );

  const handleFieldChange = (changedFields) => {
    const fieldName = changedFields[0]?.name?.[0];
    const fieldValue = changedFields[0]?.value;

    if (fieldName === 'ledgerType' && fieldValue === 'addType') {
      setOpenCustomTypeModal(true);
      form.setFieldValue('ledgerType', undefined);
    }

    if (fieldName === 'vendor' && fieldValue) {
      setVendorLoading(true);
    }
  };

  useEffect(() => {
    if (foundVendor?._id) {
      setVendorLoading(false);
      setVendor(foundVendor);
    }
  }, [foundVendor]);
  const uomOptions = useMemo(() => {
    return (
      dropdownsData?.dropdowns
        ?.find((e) => e.name === 'uom')
        ?.values?.map((option) => ({
          label: option,
          value: option,
        })) || []
    );
  }, [dropdownsData]);
  // Prefill form data when editing
  useEffect(() => {
    if (voucher?._id) {
      const formValues = {
        date: voucher?.date ? dayjs(voucher.date) : null,
        voucherId: voucher?.journalId,
        voucherType: voucher?.voucherType,
        ledgerType: voucher?.ledgerType?._id,
        remark: voucher?.remark,
        vendor: voucher?.vendor?._id,
      };

      form.setFieldsValue(formValues);

      // Set items if they exist
      if (voucher?.items && voucher.items.length > 0) {
        setItems(voucher.items);
      }

      // Set other related data
      if (voucher?.charges) {
        setCharges(voucher.charges);
      }
      if (voucher?.columnVisibility) {
        setColumnVisibility(voucher.columnVisibility);
      }
      if (voucher?.chargesVisibility) {
        setChargesVisibility(voucher.chargesVisibility);
      }
      if (voucher?.displayFormat) {
        setDisplayFormat(voucher.displayFormat);
      }
    }
  }, [voucher, form]);

  const productFormatData = useMemo(() => {
    const rv = voucher?.purchaseVoucherData || {};
    const pf = rv?.productTableFormat;
    const pfId = typeof pf === 'object' ? pf?._id : pf;
    return {
      productDetailsFromFormat: rv?.items || [],
      productChargesFromFormat: rv?.charges || {},
      productTableColumnHideStatus: rv?.columnVisibility || {},
      productTableChargesHideStatus: rv?.chargesVisibility || {},
      productTableFormat: pfId || null,
    };
  }, [voucher]);

  const handleSubmit = async (values) => {
    let transformedItems = [];
    for (let i of items) {
      let temp = {
        ...i,
      };
      transformedItems.push(temp);
    }

    let obj = {
      ...values,
      date: values.date ? values.date.format('YYYY-MM-DD') : undefined,
      items: items,
      charges,
      columnVisibility,
      chargesVisibility,
      displayFormat,
    };

    let res;
    if (editData?._id) {
      res = await editVoucher({ data: { updateData: obj, id: editData?._id } });
    } else {
      res = await createVoucher({
        data: { ...obj, voucherType: 'purchaseVoucher' },
      });
    }

    if (!res?.error) {
      setOpenModal(false);
      form.resetFields();
      setItems(
        defaultParam?.projectDefaults?.showProductFormatTable
          ? []
          : [
              {
                key: Date.now() + Math.random(),
                itemId: '',
                productName: '',
                uom: '',
                hsn: '',
                quantity: '',
                rate: '',
                discount: '',
                amount: 0,
                cgst: '',
                sgst: '',
                cgstAmount: 0,
                sgstAmount: 0,
                igst: '',
                igstAmount: 0,
                totalAmount: 0,
                color: '#FFFFFF',
              },
            ]
      );
      setCharges({});
      setColumnVisibility({});
      setChargesVisibility({});
      setDisplayFormat(null);
      toast.success(
        `Purchase Voucher ${editData?._id ? 'Updated' : 'Created'} successfully`
      );
    } else {
      toast.error(
        'Faced an error while creating voucher, please reload and try again.'
      );
    }
  };

  return (
    <>
      {openCustomTypeModal && (
        <CustomTypesModal
          type="ledgerType"
          openModal={openCustomTypeModal}
          setOpenModal={setOpenCustomTypeModal}
        />
      )}
      <div className="bg-white border border-gray-100 shadow-sm rounded-lg overflow-hidden mx-auto max-w-6xl">
        {/* Header */}
        <div className="bg-gray-50 px-4 py-3 border-b border-gray-100">
          <div className="flex items-center gap-3">
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={() => {
                setOpenModal(false);
                setEditData({});
                form.resetFields();
              }}
              type="text"
              size="small"
              className="hover:bg-gray-200"
            />
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-0">
                {isEditing ? 'Edit' : 'Create'} Purchase Voucher
              </h2>
              <p className="text-sm text-gray-600 mb-0">
                {isEditing
                  ? 'Update purchase voucher information'
                  : 'Create a new purchase voucher'}
              </p>
            </div>
          </div>
        </div>

        {/* Form Content */}
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          onFieldsChange={handleFieldChange}
          className="p-4 space-y-4"
        >
          {/* Basic Information */}
          <div className="bg-white border border-gray-200 rounded-lg p-3">
            <h3 className="text-sm font-medium text-gray-700 mb-3">
              Basic Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              <div className="space-y-1">
                <label className="text-sm font-medium text-gray-600">
                  Voucher ID
                </label>
                {editData?.length >= 24 ? (
                  <Form.Item name="voucherId" className="mb-0">
                    <div className="text-sm text-gray-900">
                      {form.getFieldValue('voucherId')}
                    </div>
                  </Form.Item>
                ) : (
                  <IdGenComp {...idCompData} />
                )}
              </div>

              <Form.Item
                name="date"
                label={
                  <span className="text-sm font-medium text-gray-600">
                    Date
                  </span>
                }
                className="mb-0"
              >
                <DatePicker
                  format={'DD-MM-YYYY'}
                  className="text-sm placeholder:text-gray-400 w-full"
                  size="middle"
                />
              </Form.Item>

              <Form.Item
                name="ledgerType"
                label={
                  <span className="text-sm font-medium text-gray-600">
                    Ledger Type
                  </span>
                }
                className="mb-0"
              >
                <SelectV2
                  name="ledgerType"
                  placeholder="Select ledger type"
                  isLoading={isLedgerTypesLoading}
                  options={[
                    { name: '+ Add Type', value: 'addType' },
                    ...(ledgerTypes?.map((item) => ({
                      name: item.name,
                      value: item._id,
                    })) || []),
                  ]}
                  className="text-sm"
                />
              </Form.Item>
            </div>
          </div>

          {/* Vendor & Purchase Order */}
          <div className="bg-white border border-gray-200 rounded-lg p-3">
            <h3 className="text-sm font-medium text-gray-700 mb-3">Vendor</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <Form.Item
                name="vendor"
                label={
                  <span className="text-sm font-medium text-gray-600">
                    Select Vendor
                  </span>
                }
                className="mb-0"
              >
                <SelectV2
                  name="vendor"
                  placeholder="Select vendor"
                  isLoading={isVendorLoading}
                  options={
                    vendors?.map((vendor) => ({
                      name: vendor.name,
                      value: vendor._id,
                    })) || []
                  }
                  className="text-sm"
                />
              </Form.Item>
            </div>
          </div>

          {/* Vendor Details */}
          {vendor && (
            <div className="bg-white border border-gray-200 rounded-lg p-3">
              <h3 className="text-sm font-medium text-gray-700 mb-3">
                Vendor Details
              </h3>
              <MasterDetails
                isLoading={vendorLoading}
                isMobile={isMobile}
                isTablet={isTablet}
                className="!text-gray-500 !text-sm"
                details={vendor || {}}
                setDetails={setVendor}
                excludedFields={[
                  'id',
                  '_id',
                  'logo',
                  '__v',
                  'profileId',
                  'createdAt',
                  'updatedAt',
                  'idFormat',
                  'isUsed',
                  'isHidden',
                  'lastUsed',
                  'additionalFields',
                  'name',
                  'idData',
                  'attachments',
                ]}
              />
            </div>
          )}

          {/* Item Details */}
          {(() => {
            const useFormatTable = isEditing
              ? !!voucher?.purchaseVoucherData?.productTableFormat
              : defaultParam?.projectDefaults?.showProductFormatTable;

            return (
              <div className="bg-white border border-gray-200 rounded-lg p-3">
                <h3 className="text-sm font-medium text-gray-700 mb-3">
                  {useFormatTable ? 'Product Format Table' : 'Product Table'}
                </h3>
                {useFormatTable ? (
                  <ProductFormatManager
                    input={items}
                    setInput={setItems}
                    charges={charges}
                    setCharges={setCharges}
                    columnVisibility={columnVisibility}
                    setColumnVisibility={setColumnVisibility}
                    chargesVisibility={chargesVisibility}
                    setChargesVisibility={setChargesVisibility}
                    displayFormat={displayFormat}
                    setDisplayFormat={setDisplayFormat}
                    isEdit={isEditing}
                    isCopy={false}
                    data={productFormatData}
                  />
                ) : (
                  <StaticProductTable
                    input={items}
                    setInput={setItems}
                    charges={charges}
                    setCharges={setCharges}
                    uomOptions={uomOptions}
                  />
                )}
              </div>
            );
          })()}

          {/* Remarks */}
          <div className="bg-white border border-gray-200 rounded-lg p-3">
            <h3 className="text-sm font-medium text-gray-700 mb-3">Remarks</h3>
            <Form.Item name="remark" className="mb-0">
              <Textarea
                name="remark"
                rows={3}
                placeholder="Enter additional remarks or comments"
                className="text-sm resize-none"
              />
            </Form.Item>
          </div>

          {/* Footer Actions */}
          <div className="bg-gray-50 px-4 py-3 border-t border-gray-100 -mx-4 -mb-4 mt-4">
            <div className="flex items-center justify-end">
              <Button
                type="primary"
                htmlType="submit"
                size="small"
                className="text-sm px-4 py-1 h-8"
              >
                {isEditing ? 'Update' : 'Save'} Purchase Voucher
              </Button>
            </div>
          </div>
        </Form>
      </div>
    </>
  );
};

export default PurchaseVoucherForm;
