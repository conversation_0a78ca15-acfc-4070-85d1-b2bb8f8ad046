import { ArrowLeftOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, DatePicker, Form } from 'antd';
import dayjs from 'dayjs';
import { useContext, useEffect, useMemo, useState } from 'react';
import { toast } from 'react-toastify';
import Input from '../../../components/global/components/Input';
import Textarea from '../../../components/global/components/Textarea';
import ProductFormatManager from '../../../components/ProductFormats/ProductFormatManager';
import StaticProductTable from '../../../components/ProductFormats/StaticProductTable';
import usePrefixIds from '../../../hooks/usePrefixIds';
import {
  useCreateVoucherMutation,
  useEditVoucherMutation,
  useGetVoucherByIdQuery,
} from '../../../slices/AccountManagement/voucherApiSlice';
import { useGetDropdownsQuery } from '../../../slices/dropdownApiSlice';
import { Store } from '../../../store/Store';

const ReceiptVoucherForm = ({ props }) => {
  const { setOpenModal, editData, setEditData } = props;
  const [form] = Form.useForm(); // Add form instance

  const [editVoucher] = useEditVoucherMutation();
  const { data: voucher } = useGetVoucherByIdQuery(
    { id: editData?._id },
    { skip: editData?._id === undefined }
  );
  const { defaults: { defaultParam } = {} } = useContext(Store);

  const isEditing = editData?._id !== undefined;

  const [createVoucher] = useCreateVoucherMutation();

  const { IdGenComp, idCompData } = usePrefixIds({
    idFor: 'receiptVoucherId',
  });

  const [items, setItems] = useState(
    defaultParam?.projectDefaults?.showProductFormatTable || isEditing
      ? []
      : [
          {
            key: Date.now() + Math.random(),
            itemId: '',
            productName: '',
            uom: '',
            hsn: '',
            quantity: '',
            rate: '',
            discount: '',
            amount: 0,
            cgst: '',
            sgst: '',
            cgstAmount: 0,
            sgstAmount: 0,
            igst: '',
            igstAmount: 0,
            totalAmount: 0,
            color: '#FFFFFF',
          },
        ]
  );

  const [charges, setCharges] = useState({});
  const [columnVisibility, setColumnVisibility] = useState({});
  const [chargesVisibility, setChargesVisibility] = useState({});
  const [displayFormat, setDisplayFormat] = useState(null);
  const { data: dropdownsData } = useGetDropdownsQuery();

  const productFormatData = useMemo(() => {
    const rv = voucher?.receiptVoucherData || {};
    const pf = rv?.productTableFormat;
    const pfId = typeof pf === 'object' ? pf?._id : pf;
    return {
      productDetailsFromFormat: rv?.items || [],
      productChargesFromFormat: rv?.charges || {},
      productTableColumnHideStatus: rv?.columnVisibility || {},
      productTableChargesHideStatus: rv?.chargesVisibility || {},
      productTableFormat: pfId || null,
    };
  }, [voucher]);

  // Prefill form when editing
  useEffect(() => {
    if (voucher?._id) {
      const formValues = {
        date: voucher?.date ? dayjs(voucher.date) : dayjs(),
        receiptVoucherId: voucher?.receiptVoucherId,
        voucherType: voucher?.voucherType,
        paymentType: voucher?.receiptVoucherData?.paymentType,
        from: voucher?.receiptVoucherData?.from || '',
        billTo: voucher?.receiptVoucherData?.billTo || '',
        remarks: voucher?.remarks || '',
      };

      form.setFieldsValue(formValues);

      // Set other state
      if (
        voucher?.receiptVoucherData?.items &&
        voucher.receiptVoucherData.items.length > 0
      ) {
        setItems(voucher.receiptVoucherData.items);
      }
      if (voucher?.receiptVoucherData?.charges) {
        setCharges(voucher.receiptVoucherData.charges);
      }
      if (voucher?.receiptVoucherData?.columnVisibility) {
        setColumnVisibility(voucher.receiptVoucherData.columnVisibility);
      }
      if (voucher?.receiptVoucherData?.chargesVisibility) {
        setChargesVisibility(voucher.receiptVoucherData.chargesVisibility);
      }
      if (voucher?.receiptVoucherData?.productTableFormat) {
        setDisplayFormat(voucher.receiptVoucherData.productTableFormat);
      }
    }
  }, [voucher, form]);

  const uomOptions = useMemo(() => {
    return (
      dropdownsData?.dropdowns
        ?.find((e) => e.name === 'uom')
        ?.values?.map((option) => ({
          label: option,
          value: option,
        })) || []
    );
  }, [dropdownsData]);

  const handleSubmit = async (values) => {
    const transformedItems = items.map((item) => ({ ...item }));
    const obj = {
      ...values,
      date: values.date?.format('YYYY-MM-DD'),
      items: transformedItems,
      idData: idCompData?.dataToReturn,
      charges,
      columnVisibility,
      chargesVisibility,
      productTableFormat: displayFormat?._id || displayFormat || null,
    };

    try {
      const res = editData?._id
        ? await editVoucher({ data: { updateData: obj, id: editData._id } })
        : await createVoucher({
            data: { ...obj, voucherType: 'receiptVoucher' },
          });

      if (!res?.error) {
        setOpenModal(false);
        form.resetFields();
        toast.success(
          `Receipt Voucher ${editData?._id ? 'Updated' : 'Created'} successfully`
        );
      } else {
        toast.error(
          'Faced an error while creating voucher, please reload and try again.'
        );
      }
    } catch (error) {
      toast.error(
        'Faced an error while creating voucher, please reload and try again.'
      );
    }
  };

  return (
    <>
      <div className="bg-white border border-gray-100 shadow-sm rounded-lg overflow-hidden mx-auto max-w-6xl">
        {/* Header */}
        <div className="bg-gray-50 px-4 py-3 border-b border-gray-100">
          <div className="flex items-center gap-3">
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={() => {
                setOpenModal(false);
                setEditData({});
              }}
              type="text"
              size="small"
              className="hover:bg-gray-200"
            />
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-0">
                {isEditing ? 'Edit' : 'Create'} Receipt Voucher
              </h2>
              <p className="text-sm text-gray-600 mb-0">
                {isEditing
                  ? 'Update receipt voucher information'
                  : 'Create a new receipt voucher'}
              </p>
            </div>
          </div>
        </div>

        {/* Form Content */}
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            date: dayjs(),
          }}
        >
          <div className="p-4 space-y-4">
            {/* Basic Information */}
            <div className="bg-white border border-gray-200 rounded-lg p-3">
              <h3 className="text-sm font-medium text-gray-700 mb-3">
                Basic Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">
                    Receipt Voucher ID
                  </label>
                  {isEditing ? (
                    <Form.Item name="receiptVoucherId">
                      <Input disabled className="text-sm bg-gray-50" />
                    </Form.Item>
                  ) : (
                    <IdGenComp {...idCompData} />
                  )}
                </div>

                <Form.Item
                  name="date"
                  label={
                    <span className="text-sm font-medium text-gray-600">
                      Date
                    </span>
                  }
                >
                  <DatePicker
                    format={'DD-MM-YYYY'}
                    className="text-sm placeholder:text-gray-400 w-full"
                    size="middle"
                  />
                </Form.Item>

                <Form.Item
                  name="paymentType"
                  label={
                    <span className="text-sm font-medium text-gray-600">
                      Payment Type
                    </span>
                  }
                >
                  <Input placeholder="Enter payment type" className="text-sm" />
                </Form.Item>
              </div>
            </div>

            {/* Receipt Details */}
            <div className="bg-white border border-gray-200 rounded-lg p-3">
              <h3 className="text-sm font-medium text-gray-700 mb-3">
                Receipt Details
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <Form.Item
                  name="from"
                  label={
                    <span className="text-sm font-medium text-gray-600">
                      From
                    </span>
                  }
                >
                  <Input placeholder="Enter sender" className="text-sm" />
                </Form.Item>

                <Form.Item
                  name="billTo"
                  label={
                    <span className="text-sm font-medium text-gray-600">
                      Bill To
                    </span>
                  }
                >
                  <Input placeholder="Enter receiver" className="text-sm" />
                </Form.Item>
              </div>
            </div>
            {/* Product Table */}
            {(() => {
              const useFormatTable = isEditing
                ? !!voucher?.receiptVoucherData?.productTableFormat
                : defaultParam?.projectDefaults?.showProductFormatTable;

              return (
                <div className="bg-white border border-gray-200 rounded-lg p-3">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">
                    {useFormatTable ? 'Product Format Table' : 'Product Table'}
                  </h3>
                  {useFormatTable ? (
                    <ProductFormatManager
                      input={items}
                      setInput={setItems}
                      charges={charges}
                      setCharges={setCharges}
                      columnVisibility={columnVisibility}
                      setColumnVisibility={setColumnVisibility}
                      chargesVisibility={chargesVisibility}
                      setChargesVisibility={setChargesVisibility}
                      displayFormat={displayFormat}
                      setDisplayFormat={setDisplayFormat}
                      isEdit={isEditing}
                      isCopy={false}
                      data={productFormatData}
                    />
                  ) : (
                    <StaticProductTable
                      input={items}
                      setInput={setItems}
                      charges={charges}
                      setCharges={setCharges}
                      uomOptions={uomOptions}
                    />
                  )}
                </div>
              );
            })()}

            {/* Remarks */}
            <div className="bg-white border border-gray-200 rounded-lg p-3">
              <h3 className="text-sm font-medium text-gray-700 mb-3">
                Remarks
              </h3>
              <Form.Item name="remarks">
                <Textarea
                  rows={3}
                  placeholder="Enter additional remarks or comments"
                  className="text-sm resize-none"
                />
              </Form.Item>
            </div>
          </div>

          {/* Footer Actions */}
          <div className="bg-gray-50 px-4 py-3 border-t border-gray-100">
            <div className="flex items-center justify-end">
              <Button
                htmlType="submit"
                type="primary"
                size="small"
                className="text-sm px-4 py-1 h-8"
              >
                {isEditing ? 'Update' : 'Save'} Receipt Voucher
              </Button>
            </div>
          </div>
        </Form>
      </div>
    </>
  );
};

export default ReceiptVoucherForm;
