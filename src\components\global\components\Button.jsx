import Spinner from './Spinner';

const style = (buttonStyle, disabled, isLoading, color, textColor, size) => {
  let sizeStyles = '';
  switch (size) {
    case 'sm':
      sizeStyles = '!py-[7px] !px-[15px] text-sm';
      break;
    default:
      sizeStyles = '';
  }
  switch (buttonStyle) {
    case 'filled':
      if (disabled || isLoading) {
        return `bg-${color}-disabled hover:cursor-not-allowed ${textColor} ${sizeStyles}`;
      } else {
        return `bg-${color}-primary hover:brightness-105 ${textColor} ${sizeStyles}`;
      }
    case 'outline':
      if (disabled || isLoading) {
        return `border-${color}-disabled border-2 text-${color}-disable hover:cursor-not-allowed ${sizeStyles}`;
      } else {
        let bg = `bg-${color}-600`;
        let border = `border-${color}-600`;
        return `${border} border-2 text-${color}-primary hover:${bg} hover:text-white ${sizeStyles}`;
      }
    default:
      if (disabled || isLoading) {
        return `bg-${color}-disabled hover:cursor-not-allowed ${textColor} ${sizeStyles}`;
      } else {
        return `bg-${color}-primary hover:brightness-105 ${textColor} ${sizeStyles}`;
      }
  }
};

const Button = ({
  children,
  type = 'button',
  buttonStyle = 'filled',
  onClick,
  color = 'blue',
  textColor = 'text-white',
  disabled,
  className,
  icon,
  size,
  isLoading = false,
  spinnerColor = '',
  spinnerSize = '4',
  ...rest
}) => {
  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled || isLoading}
      className={`h-fit items-center py-2 px-4 rounded-md outline-none overflow-hidden flex ${style(
        buttonStyle,
        disabled,
        isLoading,
        color,
        textColor,
        size
      )} ${className}`}
      {...rest}
    >
      <div className={`w-full h-full flex justify-center gap-x-2 items-center`}>
        {(isLoading || icon) && (
          <>
            <section className="aspect-square flex items-center">
              {isLoading ? (
                <Spinner
                  size={spinnerSize}
                  color={spinnerColor || 'white'}
                  width={2}
                />
              ) : icon ? (
                icon
              ) : null}
            </section>
          </>
        )}
        {children}
      </div>
    </button>
  );
};

export default Button;
