import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import {
  <PERSON>ton,
  Popconfirm,
  Space,
  Table,
  Tag,
  Tooltip,
  Typography,
} from 'antd';
import { useMediaQuery } from 'react-responsive';
import { toast } from 'react-toastify';
import { mobileWidth } from '../../../helperFunction';
import { useDeleteVoucherMutation } from '../../../slices/AccountManagement/voucherApiSlice';

const { Text } = Typography;

const PaymentVoucherTable = ({
  rows,
  isLoading,
  checkedRows,
  handleSelectAll,
  selectAll,
  handleCheckBoxChange,
  setOpenSidebar,
  setVoucherId,
  onEdit,
}) => {
  const [deleteVoucher] = useDeleteVoucherMutation();
  const isMobile = useMediaQuery({ query: mobileWidth });

  const handleDelete = async (record) => {
    try {
      const res = await deleteVoucher({ data: { id: record._id } });
      if (res.error) {
        toast.error('Failed to delete voucher. Please reload and try again.');
      } else {
        toast.success('Voucher deleted successfully');
      }
    } catch (error) {
      toast.error('An error occurred while deleting the voucher.');
    }
  };

  const getPaymentModeColor = (mode) => {
    const colors = {
      cash: 'green',
      cheque: 'blue',
      'bank transfer': 'purple',
      'online payment': 'orange',
      upi: 'cyan',
    };
    return colors[mode?.toLowerCase()] || 'default';
  };

  const columns = [
    !isMobile && {
      title: (
        <div>
          {rows?.length > 0 && (
            <label>
              <input
                type="checkbox"
                checked={selectAll}
                onChange={handleSelectAll}
                style={{ marginRight: 8 }}
              />
              Select All ({checkedRows?.length || 0})
            </label>
          )}
        </div>
      ),
      dataIndex: 'selection',
      key: 'selection',
      width: 120,
      render: (_, record) => (
        <input
          type="checkbox"
          checked={checkedRows?.includes(record)}
          onChange={(e) => {
            handleCheckBoxChange(e, record);
            e.stopPropagation();
          }}
        />
      ),
    },
    {
      title: 'Payment Voucher ID',
      dataIndex: 'paymentVoucherId',
      key: 'paymentVoucherId',
      width: 180,
      render: (text, record) => (
        <Button
          type="link"
          onClick={() => {
            setVoucherId(record._id);
            setOpenSidebar(true);
          }}
          style={{ padding: 0, height: 'auto' }}
        >
          <Text strong style={{ color: '#1890ff' }}>
            {text || '-'}
          </Text>
        </Button>
      ),
    },
    {
      title: 'Paid To',
      key: 'paidTo',
      width: 200,
      render: (_, record) => (
        <div
          onClick={() => {
            setVoucherId(record._id);
            setOpenSidebar(true);
          }}
          className="cursor-pointer"
        >
          <Text>{record?.paymentVoucherData?.paidTo || '-'}</Text>
        </div>
      ),
    },
    {
      title: 'Vendor/Customer',
      key: 'vendorCustomer',
      width: 200,
      render: (_, record) => {
        const vendor = record?.paymentVoucherData?.vendor;
        const customer = record?.paymentVoucherData?.customer;
        
        if (vendor) {
          return (
            <div>
              <Text>{vendor?.name}</Text>
              <br />
              <Tag size="small" color="blue">
                Vendor
              </Tag>
            </div>
          );
        }
        if (customer) {
          return (
            <div>
              <Text>{customer?.name}</Text>
              <br />
              <Tag size="small" color="green">
                Customer
              </Tag>
            </div>
          );
        }
        return '-';
      },
    },
    {
      title: 'Payment Mode',
      key: 'paymentMode',
      width: 150,
      render: (_, record) => (
        <Tag color={getPaymentModeColor(record?.paymentVoucherData?.paymentMode)}>
          {record?.paymentVoucherData?.paymentMode || '-'}
        </Tag>
      ),
    },
    {
      title: 'Amount',
      key: 'amount',
      width: 150,
      render: (_, record) => {
        const amount = record?.paymentVoucherData?.amount;
        return (
          <Text strong style={{ color: '#f5222d', fontSize: '16px' }}>
            ₹{amount ? Number(amount).toLocaleString() : '-'}
          </Text>
        );
      },
      sorter: (a, b) => (a?.paymentVoucherData?.amount || 0) - (b?.paymentVoucherData?.amount || 0),
    },
    {
      title: 'Ledger Type',
      key: 'ledgerType',
      width: 150,
      render: (_, record) => (
        <Tag color="volcano">
          {record?.paymentVoucherData?.ledgerType?.name || '-'}
        </Tag>
      ),
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      width: 120,
      render: (date) => new Date(date).toLocaleDateString(),
      sorter: (a, b) => new Date(a.date) - new Date(b.date),
    },
    {
      title: 'Remarks',
      dataIndex: 'remarks',
      key: 'remarks',
      width: 200,
      render: (remark) => {
        if (!remark) return '-';
        if (remark.length > 50) {
          return (
            <Tooltip title={remark} placement="topLeft">
              <Text ellipsis style={{ maxWidth: 180, display: 'block' }}>
                {remark}
              </Text>
            </Tooltip>
          );
        }
        return remark;
      },
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 150,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="Edit Payment Voucher">
            <Button
              type="text"
              icon={<EditOutlined />}
              size="small"
              onClick={() => {
                if (onEdit) {
                  onEdit(record);
                }
              }}
              style={{ color: '#52c41a' }}
            />
          </Tooltip>
          <Popconfirm
            title="Delete Payment Voucher"
            description="Are you sure you want to delete this payment voucher?"
            onConfirm={() => handleDelete(record)}
            okText="Yes"
            cancelText="No"
            okType="danger"
          >
            <Tooltip title="Delete Payment Voucher">
              <Button
                type="text"
                icon={<DeleteOutlined />}
                size="small"
                danger
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ].filter(Boolean);

  return (
    <div className="payment-voucher-table-container">
      <Table
        columns={columns}
        dataSource={rows || []}
        rowKey="_id"
        loading={isLoading}
        pagination={false}
        scroll={{ x: 1300 }}
      />
    </div>
  );
};

export default PaymentVoucherTable;
