import { ArrowLeftOutlined } from '@ant-design/icons';
import { useEffect, useMemo, useState } from 'react';
import { toast } from 'react-toastify';
import usePrefixIds from '../../../hooks/usePrefixIds';
import CustomTypesModal from '../global/CustomTypesModal';

import { Button, DatePicker, Form } from 'antd';
import dayjs from 'dayjs';
import Input from '../../../components/global/components/Input';
import SelectV2 from '../../../components/global/components/SelectV2';
import Textarea from '../../../components/global/components/Textarea';
import { useGetCustomTypesQuery } from '../../../slices/AccountManagement/customTypesApiSlice';
import {
  useCreateVoucherMutation,
  useEditVoucherMutation,
  useGetVoucherByIdQuery,
} from '../../../slices/AccountManagement/voucherApiSlice';
import { useGetAllcustomerQuery } from '../../../slices/customerDataSlice';
import { useGetAllVendorsForOptionsQuery } from '../../../slices/vendorApiSlice';

const PaymentVoucherForm = ({ props }) => {
  const { setOpenModal, editData, setEditData } = props;
  const [form] = Form.useForm();

  const [editVoucher] = useEditVoucherMutation();
  const { data: ledgerTypes, isLoading: isLedgerTypesLoading } =
    useGetCustomTypesQuery({ type: 'ledgerType' });
  const { data: vendors, isLoading: isVendorLoading } =
    useGetAllVendorsForOptionsQuery();
  const { data: customers } = useGetAllcustomerQuery();
  const { data: voucher } = useGetVoucherByIdQuery(
    { id: editData?._id },
    { skip: editData?._id === undefined }
  );

  const isEditing = editData?._id !== undefined;
  const [createVoucher] = useCreateVoucherMutation();

  const { IdGenComp, idCompData } = usePrefixIds({
    idFor: 'paymentVoucherId',
  });

  const [openCustomTypeModal, setOpenCustomTypeModal] = useState(false);

  // Set initial form values
  useEffect(() => {
    if (!isEditing) {
      form.setFieldsValue({
        date: dayjs(),
      });
    }
  }, [form, isEditing]);

  // Pre-fill form data when editing
  useEffect(() => {
    if (voucher?._id) {
      const vendorId = voucher?.paymentVoucherData?.vendor?._id;
      const customerId = voucher?.paymentVoucherData?.customer?._id;
      const formValues = {
        date: voucher?.date ? dayjs(voucher.date) : dayjs(),
        paymentVoucherId: voucher?.paymentVoucherId,
        ledgerType: voucher?.paymentVoucherData?.ledgerType?._id,
        vendor: vendorId,
        customer: customerId,
        vendorCustomer: vendorId || customerId,
        paymentMode: voucher?.paymentVoucherData?.paymentMode,
        paidTo: voucher?.paymentVoucherData?.paidTo,
        amount: voucher?.paymentVoucherData?.amount,
        remarks: voucher?.remarks,
      };

      form.setFieldsValue(formValues);
    }
  }, [voucher, form]);

  const vendorCustomerOptions = useMemo(() => {
    let options = [];
    if (vendors?.length > 0) {
      let temp = vendors.map((vendor) => ({
        label: vendor.name,
        value: vendor._id,
        type: 'vendor',
      }));
      options = [...options, ...temp];
    }
    if (customers?.customers?.length > 0) {
      let temp = customers.customers.map((customer) => ({
        label: customer.name,
        value: customer._id,
        type: 'customer',
      }));
      options = [...options, ...temp];
    }
    return options;
  }, [vendors, customers]);

  const handleLedgerTypeChange = (value) => {
    if (value === 'addType') {
      setOpenCustomTypeModal(true);
      form.setFieldValue('ledgerType', undefined);
    }
  };

  const handleVendorCustomerChange = (selectedId) => {
    const foundVendor = vendors?.find((v) => v?._id === selectedId);
    const foundCustomer = customers?.customers?.find(
      (c) => c?._id === selectedId
    );

    if (foundVendor) {
      form.setFieldsValue({
        vendorCustomer: selectedId,
        vendor: selectedId,
        customer: undefined,
      });
    } else if (foundCustomer) {
      form.setFieldsValue({
        vendorCustomer: selectedId,
        customer: selectedId,
        vendor: undefined,
      });
    } else {
      form.setFieldsValue({
        vendorCustomer: undefined,
        vendor: undefined,
        customer: undefined,
      });
    }
  };

  const handleSubmit = async (values) => {
    try {
      const chosenId = values.vendorCustomer;
      const isVendor = vendors?.some((v) => v._id === chosenId);
      const isCustomer = customers?.customers?.some((c) => c._id === chosenId);

      const submitData = {
        date: values.date?.toISOString?.() || dayjs(values.date).toISOString(),
        paymentVoucherData: {
          ledgerType: values.ledgerType,
          vendor: isVendor ? chosenId : undefined,
          customer: isCustomer ? chosenId : undefined,
          paymentMode: values.paymentMode,
          paidTo: values.paidTo,
          amount: Number(values.amount),
        },
        remarks: values.remarks,
        voucherType: 'paymentVoucher',
        idData: idCompData?.dataToReturn,
      };

      let res;
      if (editData?._id) {
        res = await editVoucher({
          data: { updateData: submitData, id: editData._id },
        });
      } else {
        res = await createVoucher({ data: submitData });
      }

      if (!res?.error) {
        setOpenModal(false);
        form.resetFields();
        toast.success(
          `Payment Voucher ${editData?._id ? 'Updated' : 'Created'} successfully`
        );
      } else {
        toast.error(
          'Faced an error while creating voucher, please reload and try again.'
        );
      }
    } catch (error) {
      toast.error('An error occurred while processing the form.');
    }
  };

  return (
    <>
      {openCustomTypeModal && (
        <CustomTypesModal
          type="ledgerType"
          openModal={openCustomTypeModal}
          setOpenModal={setOpenCustomTypeModal}
        />
      )}

      <div className="bg-white border border-gray-100 shadow-sm rounded-lg overflow-hidden mx-auto max-w-6xl">
        {/* Header */}
        <div className="bg-gray-50 px-4 py-3 border-b border-gray-100">
          <div className="flex items-center gap-3">
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={() => {
                setOpenModal(false);
                setEditData({});
              }}
              type="text"
              size="small"
              className="hover:bg-gray-200"
            />
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-0">
                {isEditing ? 'Edit' : 'Create'} Payment Voucher
              </h2>
              <p className="text-sm text-gray-600 mb-0">
                {isEditing
                  ? 'Update payment voucher information'
                  : 'Create a new payment voucher'}
              </p>
            </div>
          </div>
        </div>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          size="middle"
        >
          <div className="p-4 space-y-4">
            {/* Basic Information */}
            <div className="bg-white border border-gray-200 rounded-lg p-3">
              <h3 className="text-sm font-medium text-gray-700 mb-3">
                Basic Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                <div className="space-y-1">
                  <label className="text-sm font-medium text-gray-600">
                    Payment Voucher ID
                  </label>
                  {isEditing ? (
                    <Form.Item name="paymentVoucherId" className="mb-0">
                      <Input disabled className="text-sm bg-gray-50" />
                    </Form.Item>
                  ) : (
                    <IdGenComp {...idCompData} />
                  )}
                </div>

                <Form.Item
                  name="date"
                  label={
                    <span className="text-sm font-medium text-gray-600">
                      Date
                    </span>
                  }
                  rules={[{ required: true, message: 'Please select date' }]}
                  className="mb-0"
                >
                  <DatePicker
                    format="DD-MM-YYYY"
                    className="text-sm placeholder:text-gray-400 w-full"
                    placeholder="Select date"
                  />
                </Form.Item>

                <Form.Item
                  name="ledgerType"
                  label={
                    <span className="text-sm font-medium text-gray-600">
                      Ledger Type
                    </span>
                  }
                  rules={[
                    { required: true, message: 'Please select ledger type' },
                  ]}
                  className="mb-0"
                >
                  <SelectV2
                    placeholder="Select ledger type"
                    loading={isLedgerTypesLoading}
                    onChange={(e) => handleLedgerTypeChange(e.target.value)}
                    name="ledgerType"
                    className="text-sm"
                    menuPosition="fixed"
                    options={[
                      { name: '+ Add Type', value: 'addType' },
                      ...(ledgerTypes?.map((item) => ({
                        name: item.name,
                        value: item._id,
                      })) || []),
                    ]}
                  />
                </Form.Item>
              </div>
            </div>

            {/* Vendor/Customer */}
            <div className="bg-white border border-gray-200 rounded-lg p-3">
              <h3 className="text-sm font-medium text-gray-700 mb-3">
                Vendor/Customer Selection
              </h3>
              <div className="grid grid-cols-1 gap-3">
                <Form.Item
                  name="vendorCustomer"
                  label={
                    <span className="text-sm font-medium text-gray-600">
                      Vendor/Customer
                    </span>
                  }
                  className="mb-0"
                >
                  <SelectV2
                    placeholder="Select vendor or customer"
                    loading={isVendorLoading}
                    onChange={(e) => handleVendorCustomerChange(e.target.value)}
                    name="vendorCustomer"
                    className="text-sm"
                    menuPosition="fixed"
                    options={vendorCustomerOptions.map((option) => ({
                      label: option.label,
                      value: option.value,
                    }))}
                  />
                </Form.Item>
              </div>
            </div>
            {/* Payment Details */}
            <div className="bg-white border border-gray-200 rounded-lg p-3">
              <h3 className="text-sm font-medium text-gray-700 mb-3">
                Payment Details
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <Form.Item
                  name="paymentMode"
                  label={
                    <span className="text-sm font-medium text-gray-600">
                      Payment Mode
                    </span>
                  }
                  rules={[
                    { required: true, message: 'Please enter payment mode' },
                  ]}
                  className="mb-0"
                >
                  <Input placeholder="Enter payment mode" className="text-sm" />
                </Form.Item>

                <Form.Item
                  name="paidTo"
                  label={
                    <span className="text-sm font-medium text-gray-600">
                      Paid To
                    </span>
                  }
                  rules={[{ required: true, message: 'Please enter paid to' }]}
                  className="mb-0"
                >
                  <Input placeholder="Enter paid to" className="text-sm" />
                </Form.Item>

                <Form.Item
                  name="amount"
                  label={
                    <span className="text-sm font-medium text-gray-600">
                      Amount
                    </span>
                  }
                  rules={[{ required: true, message: 'Please enter amount' }]}
                  className="mb-0"
                >
                  <Input
                    type="number"
                    placeholder="Enter amount"
                    className="text-sm"
                  />
                </Form.Item>
              </div>
            </div>

            {/* Remarks */}
            <div className="bg-white border border-gray-200 rounded-lg p-3">
              <h3 className="text-sm font-medium text-gray-700 mb-3">
                Remarks
              </h3>
              <Form.Item name="remarks" className="mb-0">
                <Textarea
                  rows={3}
                  placeholder="Enter additional remarks or comments"
                  className="text-sm resize-none"
                />
              </Form.Item>
            </div>
          </div>

          {/* Footer Actions */}
          <div className="bg-gray-50 px-4 py-3 border-t border-gray-100">
            <div className="flex items-center justify-end gap-2">
              <Button
                onClick={() => {
                  setOpenModal(false);
                  setEditData({});
                }}
                size="small"
                className="text-sm px-4 py-1 h-8"
              >
                Cancel
              </Button>
              <Button
                htmlType="submit"
                type="primary"
                size="small"
                className="text-sm px-4 py-1 h-8"
              >
                {isEditing ? 'Update' : 'Save'} Payment Voucher
              </Button>
            </div>
          </div>
        </Form>
      </div>
    </>
  );
};

export default PaymentVoucherForm;
