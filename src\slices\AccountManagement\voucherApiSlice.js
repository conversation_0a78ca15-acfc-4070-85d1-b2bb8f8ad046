import { apiSlice } from '../apiSlice';

const baseRoute = '/v1/voucher';
export const voucherApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    queryVoucher: builder.query({
      query: ({
        page,
        limit,
        field = '',
        type = '',
        filters,
        searchTerm = '',
        voucherType = 'purchaseVoucher',
      }) =>
        baseRoute +
        `?page=${page}&limit=${limit}&field=${field}&type=${type}&filters=${filters}&searchTerm=${searchTerm}&voucherType=${voucherType}`,
      providesTags: ['Voucher'],
    }),
    getVoucherById: builder.query({
      query: ({ id }) => baseRoute + `/${id}`,
      providesTags: ['Voucher'],
    }),
    createVoucher: builder.mutation({
      query: ({ data }) => ({
        url: baseRoute,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Voucher'],
    }),
    editVoucher: builder.mutation({
      query: ({ data }) => ({
        url: baseRoute,
        method: 'PATCH',
        body: data,
      }),
      invalidatesTags: ['Voucher'],
    }),
    deleteVoucher: builder.mutation({
      query: ({ data }) => ({
        url: baseRoute + '/delete',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Voucher'],
    }),
    deleteManyVoucher: builder.mutation({
      query: ({ data }) => ({
        url: baseRoute + '/deleteMany',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Voucher'],
    }),
    getFilterOptions: builder.query({
      query: ({ voucherType = 'purchaseVoucher' }) =>
        baseRoute + `/filterOptions?voucherType=${voucherType}`,
      providesTags: ['Voucher'],
    }),
  }),
});

export const {
  useQueryVoucherQuery,
  useCreateVoucherMutation,
  useDeleteVoucherMutation,
  useDeleteManyVoucherMutation,
  useGetVoucherByIdQuery,
  useEditVoucherMutation,
  useGetFilterOptionsQuery,
} = voucherApi;
