import { useContext } from 'react';
import { BsThreeDotsVertical } from 'react-icons/bs';
import { CiFilter } from 'react-icons/ci';
import {
  IoIosAddCircle,
  IoMdArrowDropleftCircle,
  IoMdArrowDroprightCircle,
  IoMdPricetags,
} from 'react-icons/io';
import { MdArchive, MdOutlineArchive, MdViewAgenda } from 'react-icons/md';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { Store } from '../../store/Store';

import { Popover } from 'antd';
import { generatePrefixId } from '../../helperFunction';
import { useEditUserMutation } from '../../slices/userApiSlice';

const ColumnOptions = ({
  index,
  colCheck,
  setColCheck,
  column,
  showArchive,
  setShowArchive,
  setShowFilter,
  setActualDeletedTags,
  setDeletedTags,
  selectedTags,
  adminView,
  applyFilter,
  setAllInputs,
  setShowTagModal,
}) => {
  const { state, defaults } = useContext(Store);
  const navigate = useNavigate();
  const user = state?.user;

  const [editUser] = useEditUserMutation();

  const handleMinimize = async (val) => {
    setColCheck((prev) => ({
      ...prev,
      minimized: val,
    }));
    let realTimeUser = JSON.parse(localStorage.getItem('user'));
    realTimeUser = {
      ...realTimeUser,
      user: {
        ...realTimeUser?.user,
        collapsedColumns: {
          ...realTimeUser?.user?.collapsedColumns,
          [column?.label]: val,
        },
      },
    };
    const res = await editUser({
      id: realTimeUser?.user?._id,
      data: {
        collapsedColumns: realTimeUser?.user?.collapsedColumns,
      },
    });
    if (res) {
      localStorage.setItem('user', JSON.stringify(realTimeUser));
    } else {
      toast.error(
        `Error ${val ? 'minimizing' : 'expanding'} ${column?.label}`,
        {
          theme: 'colored',
          position: 'top-right',
          toastId: 'Empty File',
        }
      );
    }
  };

  return (
    <div>
      {colCheck?.minimized ? (
        <IoMdArrowDroprightCircle
          className="w-4 h-4 text-orange-600 cursor-pointer hover:text-orange-700 transition"
          onClick={() => {
            handleMinimize(false);
          }}
          data-tooltip-id="ExpandColumn"
          data-tooltip-place="bottom"
        />
      ) : (
        <div className="flex gap-[2px] mt-[-2px]">
          <IoMdArrowDropleftCircle
            className="w-4 h-4 text-orange-600 cursor-pointer hover:text-orange-700 transition"
            onClick={() => {
              handleMinimize(true);
            }}
            data-tooltip-id="CollapseColumn"
            data-tooltip-place="bottom"
          />
          {!adminView && column?.label !== 'Completed' && (
            <IoIosAddCircle
              className={`w-4 h-4 text-blue-600 transition ${
                colCheck?.disabled
                  ? 'cursor-not-allowed hover:text-blue-400'
                  : 'cursor-pointer hover:text-blue-700'
              }`}
              onClick={() => {
                let taskId = generatePrefixId(
                  defaults?.defaultParam?.prefixIds?.['taskId']
                );
                if (
                  !colCheck?.disabled &&
                  defaults?.defaultParam?.projectDefaults?.allowCustomKanbanId
                ) {
                  setAllInputs((prev) => ({
                    ...prev,
                    isCustomKanbanIdModalOpen: true,
                  }));
                } else if (
                  !colCheck?.disabled &&
                  !defaults?.defaultParam?.projectDefaults?.allowCustomKanbanId
                ) {
                  column?.value === '/purchase/indent/createindent'
                    ? navigate('/purchase/indent')
                    : navigate(
                        `${column?.value}?kanban=true&department=${column?.data?.selectedDepartment?.name}&page=${column?.label}&index=${index}&refType=${column?.label}&taskID=${taskId}&orderId=${''}`
                      );
                }
              }}
              data-tooltip-id="AddEntry"
              data-tooltip-place="bottom"
            />
          )}
          {column?.label !== 'Completed' && (
            <MdViewAgenda
              className={`w-4 h-4 text-blue-600 transition cursor-pointer hover:text-blue-700 !pr-[2px]`}
              onClick={() => {
                navigate(`${column?.value}`);
              }}
              data-tooltip-id="PageNavigator"
              data-tooltip-place="bottom"
            />
          )}
          <div className="relative">
            <Popover
              trigger="click"
              placement="bottomRight"
              zIndex={1}
              content={
                <div className="">
                  <ul className="divide-y divide-gray-100">
                    <li
                      className="flex items-center gap-3 py-3 px-4 hover:bg-gray-50  transition-colors duration-150 cursor-pointer"
                      onClick={() => {
                        setShowArchive((prev) => !prev);
                      }}
                    >
                      {showArchive ? (
                        <MdArchive className="w-4.5 h-4.5 text-blue-600  flex-shrink-0" />
                      ) : (
                        <MdOutlineArchive className="w-4.5 h-4.5 text-blue-600 flex-shrink-0" />
                      )}
                      <span className="text-sm font-medium text-gray-800 ">
                        {showArchive ? 'Show Unarchived' : 'Show Archived'}
                      </span>
                    </li>

                    {column?.label !== 'Completed' &&
                      (user?.role === 'admin' ||
                        user?.role === 'superuser') && (
                        <li
                          className="flex items-center gap-3 py-3 px-4 hover:bg-gray-50  transition-colors duration-150 cursor-pointer"
                          onClick={() => {
                            setShowTagModal(true);
                            setActualDeletedTags([]);
                            setDeletedTags([]);
                          }}
                        >
                          <IoMdPricetags className="w-4.5 h-4.5 text-blue-600  flex-shrink-0" />
                          <span className="text-sm font-medium text-gray-800 ">
                            Assign Tags
                          </span>
                        </li>
                      )}

                    {column?.label !== 'Completed' && (
                      <li
                        className="flex items-center gap-3 py-3 px-4 hover:bg-gray-50  transition-colors duration-150 cursor-pointer"
                        onClick={() => {
                          setShowFilter((prev) => !prev);
                        }}
                      >
                        <div
                          className="relative flex-shrink-0"
                          onClick={(e) => {
                            e.stopPropagation();
                          }}
                        >
                          {selectedTags?.length === 0 && applyFilter && (
                            <div className="h-4 w-4 text-[10px] font-bold top-[-6px] right-[-6px] rounded-full absolute bg-red-500 text-white flex items-center justify-center shadow-sm">
                              {selectedTags?.length}
                            </div>
                          )}
                          <CiFilter
                            className="w-4.5 h-4.5 text-blue-600"
                            data-tooltip-id="FilterTags"
                            data-tooltip-place="bottom"
                          />
                        </div>
                        <span className="text-sm font-medium text-gray-800 ">
                          Filter
                        </span>
                      </li>
                    )}
                  </ul>
                </div>
              }
            >
              <BsThreeDotsVertical
                className={`w-4 h-4 text-blue-600 transition cursor-pointer hover:text-blue-700 !pr-[2px]`}
              />
            </Popover>
          </div>
        </div>
      )}
    </div>
  );
};

export default ColumnOptions;
