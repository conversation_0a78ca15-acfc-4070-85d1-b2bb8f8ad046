import { Dropdown } from 'antd';
import { useContext, useEffect, useState } from 'react';
import { BsCalendar3WeekFill, BsPencilFill } from 'react-icons/bs';
import { FaArrowAltCircleRight, FaUserCircle } from 'react-icons/fa';
import { HiOutlineDotsVertical } from 'react-icons/hi';
import { TiTick } from 'react-icons/ti';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { apiSlice } from '../../slices/apiSlice';
import { Store } from '../../store/Store';

import Spinner from '../global/components/Spinner';
import DelayModal from './DelayModal';
import ShiftModal from './ShiftModal';
import Tags from './Tags';

import { addDurationToDate, generatePrefixId } from '../../helperFunction';
import { useLazyCanForwardCardQuery } from '../../slices/kanbanApiSlice';
import {
  useAddCustomTagsMutation,
  useAddMediaMutation,
  useArchiveOrderMutation,
  useDeleteMediaMutation,
  useDeleteOrderMutation,
  usePartiallyDoneMutation,
  useShiftKanbanColumnMutation,
} from '../../slices/orderApiSlice';
import { renderCard } from './cardRenderer';
import {
  canUpgrade,
  getNextColumns,
  getStep,
  handleArchive,
  handleDone,
  handleForward,
  onUpgrade,
} from './kanbanFunctions';
// import ReminderModal from './ReminderModal';
import TaskReminderModal from './TaskReminderModal';
import UserAssignModalV2 from './UserAssignModal.v2';

// let blueTagClass =
//   'bg-blue-100 cursor-pointer hover:bg-blue-500 hover:text-white transition text-blue-500 text-sm px-3 py-1 rounded-lg font-medium whitespace-nowrap';

const CardV2 = ({
  column,
  card,
  setHistorySidebar,
  tooltipIds,
  tile = false,
  setInitialScrollIndex,
  index,
  deletedTags,
  // columnNumber,
}) => {
  const navigate = useNavigate();
  const [addMedia] = useAddMediaMutation();
  const [partiallyDone] = usePartiallyDoneMutation();
  const [shiftKanbanColumn, { isLoading: shiftLoading }] =
    useShiftKanbanColumnMutation();
  const [addCustomTags] = useAddCustomTagsMutation();
  const [archiveOrder] = useArchiveOrderMutation();
  const [deleteMedia] = useDeleteMediaMutation();

  const { defaults: defaultParam, dispatch, state } = useContext(Store);
  const isSingleColumnMode =
    defaultParam?.defaultParam?.projectDefaults?.shiftToSingleDepartment;
  const [openShiftModal, setOpenShiftModal] = useState({
    open: false,
    isPartial: false,
  });
  const [selectedColumns, setSelectedColumns] = useState([]);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [isReasonModalOpen, setIsReasonModalOpen] = useState(false);
  const [reasonForDelay, setReasonForDelay] = useState([
    { reason: '', delayDate: new Date().toISOString().split('T')[0] },
  ]);
  const [userModal, setUserModal] = useState(false);
  const [flowArray, setFlowArray] = useState([]);
  const [showSelectTags, setShowSelectTags] = useState(false);
  const [selectedTags, setSelectedTags] = useState([]);
  const [remainingTime, setRemainingTime] = useState({
    hours: 0,
    minutes: 0,
    seconds: 0,
    days: 0,
    isExceed: false,
  });
  const [reminderModal, setReminderModal] = useState(false);

  const [canForwardCard] = useLazyCanForwardCardQuery();
  const [deleteOrder] = useDeleteOrderMutation();

  const dispatchFn = useDispatch();

  useEffect(() => {
    setFlowArray(
      defaultParam?.departmentFlow?.nodeStructure?.nodes?.map((item) => ({
        ...item?.data?.selectedPage?.[0],
        title: column?.title,
      }))
    );
  }, [card?.currentStep, column, defaultParam]);

  useEffect(() => {
    if (defaultParam) {
      const nextColumns = getNextColumns({ defaultParam, column });
      if (isSingleColumnMode) {
        setSelectedColumns([nextColumns?.[0]?.label]);
      } else {
        setSelectedColumns(nextColumns?.map((item) => item.label));
      }
    }
  }, [defaultParam, setSelectedColumns, column, isSingleColumnMode]);

  useEffect(() => {
    const duration = column?.data?.duration;

    // getting shift start hour
    const shiftStart = +defaultParam?.defaultParam?.shiftStart?.split(':')[0];
    const shiftStartMinute =
      +defaultParam?.defaultParam?.shiftStart?.split(':')[1];
    // getting shift end hour
    const shiftEnd = +defaultParam?.defaultParam?.shiftStop?.split(':')[0];
    const shiftEndMinute =
      +defaultParam?.defaultParam?.shiftStop?.split(':')[1];
    const creationTime =
      card?.createdAt === card?.updatedAt ? card?.createdAt : card?.updatedAt;

    if (duration) {
      const endTime = addDurationToDate(creationTime, duration);
      const intervalId = setInterval(() => {
        const currentTime = new Date();

        const withinShiftHours =
          currentTime.getHours() >= shiftStart &&
          (currentTime.getHours() < shiftEnd ||
            (currentTime.getHours() === shiftEnd &&
              currentTime.getMinutes() < shiftEndMinute));
        const withinShiftMinutes =
          currentTime.getMinutes() >= shiftStartMinute ||
          currentTime.getHours() > shiftStart;

        if (withinShiftHours && withinShiftMinutes) {
          const difference = endTime - currentTime;
          const updatedDate = new Date(Math.abs(difference));

          const days = Math.floor(Math.abs(difference) / (1000 * 60 * 60 * 24));
          const hours = updatedDate.getUTCHours();
          const minutes = updatedDate.getUTCMinutes();
          const seconds = updatedDate.getUTCSeconds();

          if (difference <= 0) {
            setRemainingTime({
              hours,
              minutes,
              seconds,
              days,
              isExceed: true,
            });
          } else {
            setRemainingTime({
              hours,
              minutes,
              seconds,
              days,
              isExceed: false,
            });
          }
        }
      }, 1000);

      return () => clearInterval(intervalId);
    }
  }, [column?.data?.duration, card?.createdAt, card?.updatedAt, defaultParam]);

  useEffect(() => {
    const handleUpdateTags = async () => {
      const prevTags = card?.tags;
      const updatedTags = prevTags.filter((tag) => !deletedTags.includes(tag));
      const sentFormat = updatedTags?.map((tag) => ({
        value: tag,
        label: tag,
      }));
      await addCustomTags({ _id: card?._id, tags: sentFormat });
      setInitialScrollIndex(index);
    };
    (async () => {
      if (deletedTags?.length > 0) {
        await handleUpdateTags();
      }
    })();
  }, [deletedTags, card, setInitialScrollIndex, index, addCustomTags]);

  useEffect(() => {
    if (card?.tags?.length > 0) {
      const tagsArray = card?.tags;
      setSelectedTags(
        tagsArray?.map((tag) => ({
          label: tag?.name,
          value: tag,
        }))
      );
    }
  }, [card, deletedTags, setSelectedTags]);

  const handleFileChange = async (file) => {
    try {
      if (file) {
        await addMedia({ id: card?._id, file });
        toast.success('Media Added Successfully');
      }
    } catch (error) {
      toast.error(error);
    }
  };

  const handleDeleteMedia = async (id) => {
    try {
      await deleteMedia({ id: card?._id, mediaId: id });
      toast.success('Media Deleted Successfully');
    } catch (error) {
      toast.error(error);
    }
  };

  const openHistorySidebar = () => {
    setHistorySidebar({
      open: true,
      steps: card?.steps,
      delayReasons: card?.delayReasons,
      orderId: card?._id,
      columnPage: column?.label,
    });
  };

  const cardArchiveOptions = [
    {
      key: 'archive',
      label: <p>Archive</p>,
    },
  ];

  const cardUnArchiveOptions = [
    {
      key: 'unarchive',
      label: <p>Unarchive</p>,
    },
  ];

  const archiveTile = async (key) => {
    if (key?.key === 'delete') {
      const deletedOrders = await deleteOrder({ data: [card?._id] });
      if (deletedOrders) toast.success('Tile deleted');
    } else {
      handleArchive({
        id: card?._id,
        archiveOrder,
        user: state?.user,
        card,
        apiSlice,
        dispatchFn,
      });
    }
  };

  return (
    <>
      {openShiftModal?.open && (
        <ShiftModal
          openShiftModal={openShiftModal}
          setOpenShiftModal={setOpenShiftModal}
          selectedColumns={selectedColumns}
          setSelectedColumns={setSelectedColumns}
          column={column}
          partiallyDone={partiallyDone}
          dispatch={dispatch}
          shiftKanbanColumn={shiftKanbanColumn}
          defaultParam={defaultParam}
          card={card}
          shiftLoading={shiftLoading}
          isSingleColumnMode={isSingleColumnMode}
        />
      )}
      {isReasonModalOpen && (
        <DelayModal
          setIsReasonModalOpen={setIsReasonModalOpen}
          column={column}
          card={card}
          defaultParam={defaultParam}
          shiftKanbanColumn={shiftKanbanColumn}
          dispatch={dispatch}
          reasonForDelay={reasonForDelay}
          selectedColumns={selectedColumns}
          setReasonForDelay={setReasonForDelay}
          setSelectedColumns={setSelectedColumns}
        />
      )}
      {shiftLoading ? (
        <div className="bg-gray-200 h-[400px] w-full text-gray-200 rounded-xl animate-pulse">
          <Spinner />
        </div>
      ) : (
        <>
          <div
            className={`${userModal ? 'z-50' : ''} shadow-md py-[2px] text-xs bg-white rounded-xl border flex flex-col gap-y-1 ${tile === true ? 'w-full' : '!w-[100%]'}`}
          >
            <div
              className={`items-start justify-between ${tile === true ? 'flex flex-col  w-full' : 'flex flex-col'}`}
            >
              {renderCard({
                data: getStep({ card, column }),
                firstStep: card?.steps?.[0],
                updatedAt: card?.updatedAt,
                column,
                taskId: card?.taskId,
                customTaskId: card?.customTaskId,
                tile,
                page: flowArray
                  ? flowArray[
                      flowArray?.findIndex(
                        (item) => item?.label === column?.page?.label
                      )
                    ]
                  : '',
                setHistorySidebar,
                openHistorySidebar,
                card,
                selectedTags,
                handleFileChange,
                media: card?.media,
                tooltipIds,
                setShowSelectTags,
                handleDeleteMedia,
              })}
              <div
                className={`items-center ml-2 gap-2 justify-center w-fit flex`}
              >
                {
                  // canUpgrade({
                  //   card,
                  //   column,
                  // }) &&
                  column?.label !== 'Completed' && tile !== true && (
                    <button
                      onClick={() => {
                        onUpgrade({
                          card,
                          column,
                          dispatch,
                          generatePrefixId,
                          navigate,
                          defaultParam,
                        });
                      }}
                      className="transition my-2 bg-blue-500 text-white border-2 border-blue-500 !py-[2px] !px-[5px] rounded-xl hover:text-blue-500 hover:bg-white hover:border-blue-500 min-w-fit"
                    >
                      <div className="flex items-center gap-1">
                        <p className="mr-1 text-[12px]">Edit</p>
                        <BsPencilFill className="text-[8px]" />
                      </div>
                    </button>
                  )
                }

                {!canUpgrade({ card, column }) &&
                column?.page?.label === 'Completed' &&
                tile === true ? (
                  <button
                    className="transition mx-auto my-2 bg-white text-white border-white !py-[2px] !px-[5px] rounded-xl min-w-fit cursor-auto"
                    disabled
                  >
                    <div className="flex items-center gap-1">
                      <p className="mr-1 text-[12px]">Edit</p>
                      <BsPencilFill className="text-[8px]" />
                    </div>
                  </button>
                ) : null}

                {canUpgrade({
                  card,
                  column,
                }) &&
                  column?.page?.label !== 'Completed' &&
                  tile === true && (
                    <button
                      onClick={() => {
                        onUpgrade({
                          card,
                          column,
                          dispatch,
                          generatePrefixId,
                          navigate,
                          defaultParam,
                        });
                      }}
                      className="transition mx-auto my-2 bg-blue-500 text-white border-2 border-blue-500 !py-[2px] !px-[5px] rounded-xl hover:text-blue-500 hover:bg-white hover:border-blue-500 min-w-fit"
                    >
                      <div className="flex items-center">
                        <p className="mr-1 text-[12px]">Edit</p>
                        <BsPencilFill />
                      </div>
                    </button>
                  )}
                {column?.label !== 'Completed' && (
                  <button
                    onClick={() =>
                      handleDone({
                        setInitialScrollIndex,
                        column,
                        card,
                        defaultParam,
                        remainingTime,
                        setReasonForDelay,
                        setIsReasonModalOpen,
                        selectedColumns,
                        partiallyDone,
                        setOpenShiftModal,
                        dispatch,
                        shiftKanbanColumn,
                        toast,
                        index,
                        canForwardCard,
                      })
                    }
                    className="transition  my-2 bg-purple-500 text-white border-2 border-purple-500 !py-[2px] px-[5px] rounded-xl hover:text-purple-500 hover:bg-white hover:border-purple-500 min-w-fit"
                  >
                    <div className="flex items-center">
                      <p className="mr-1 text-[12px]">Done</p>
                      <TiTick />
                    </div>
                  </button>
                )}
                {column?.label !== 'Completed' && (
                  <button
                    onClick={() => {
                      handleForward({
                        setInitialScrollIndex,
                        column,
                        card,
                        defaultParam,
                        remainingTime,
                        setReasonForDelay,
                        setIsReasonModalOpen,
                        selectedColumns,
                        partiallyDone,
                        setOpenShiftModal,
                        dispatch,
                        shiftKanbanColumn,
                        toast,
                        index,
                        canForwardCard,
                      });
                    }}
                    className="transition mx-auto my-2 text-[10px] bg-fuchsia-400 text-white border-2 border-fuchsia-400 !py-[2px] px-[5px] rounded-xl hover:text-fuchsia-400 hover:bg-white hover:border-fuchsia-400 min-w-fit"
                  >
                    <div className="flex items-center">
                      <p className="mr-1 text-[12px]">Forward</p>
                      <FaArrowAltCircleRight />
                    </div>
                  </button>
                )}
              </div>
            </div>
            <div
              className={`items-center mx-3 gap-2 justify-center w-fit flex`}
            >
              {/* {card?.tags?.length > 1 ? (
                <span className={blueTagClass} title={card?.tags.join(' ,')}>
                  {card?.tags[0]} (+{card?.tags.length - 1})
                </span>
              ) : card?.tags?.length !== 0 ? (
                <span className={blueTagClass}>{card?.tags[0]}</span>
              ) : (
                ''
              )} */}
            </div>
            <hr className={`${tile === true ? 'hidden' : ''}`} />
            <div
              className={`${tile === true ? 'hidden' : ''} flex items-center justify-between pl-2 py-[5px]`}
            >
              <div className="w-fit ml-[2%]  !text-[9px]">
                {column?.data?.duration && (
                  <p
                    className={`${remainingTime.isExceed ? 'text-red-500' : 'text-green-500'} font-semibold`}
                  >
                    {remainingTime.days} days {remainingTime.hours} hrs{' '}
                    {remainingTime.minutes} mins {remainingTime.seconds} secs
                  </p>
                )}
              </div>
              <div className="flex items-center justify-around gap-2 mr-2">
                <div className="relative">
                  <BsCalendar3WeekFill
                    className="cursor-pointer text-xl text-slate-300"
                    onClick={() => {
                      setReminderModal(!reminderModal);
                      if (userModal) {
                        setUserModal(false);
                      }
                    }}
                  />
                  {card?.reminderDate && (
                    <p className="absolute text-[8px] -top-2 -right-2 px-1.5 aspect-square flex items-center justify-center font-semibold rounded-full bg-red-500 text-white"></p>
                  )}
                </div>
                <div className="relative">
                  <FaUserCircle
                    className="cursor-pointer text-2xl text-slate-300"
                    onClick={() => {
                      setUserModal(!userModal);
                      if (reminderModal) setReminderModal(false);
                    }}
                  />
                  {selectedUsers?.length > 0 && (
                    <p className="absolute text-[8px] -top-2 -right-2 px-1.5 aspect-square flex items-center justify-center font-semibold rounded-full bg-red-500 text-white">
                      <span className="mt-[1px]">{selectedUsers?.length}</span>
                    </p>
                  )}
                </div>
                {/* <FaClock
                  className="cursor-pointer"
                  onClick={() => {
                    setReminderModal(!reminderModal);
                    if (userModal) {
                      setUserModal(false);
                    }
                  }}
                /> */}
                {/* ANCHOR */}
                <Dropdown
                  menu={{
                    items: [
                      state?.user?.role === 'admin' ||
                        (state?.user?.role === 'superuser' && {
                          label: <p>Delete</p>,
                          key: 'delete',
                        }),
                      ...(card?.archive?.includes(state?.user?._id)
                        ? cardUnArchiveOptions
                        : cardArchiveOptions),
                    ],
                    onClick: archiveTile,
                  }}
                  placement="bottomLeft"
                  trigger={['click']}
                >
                  <p>
                    <HiOutlineDotsVertical className="text-xl cursor-pointer" />
                  </p>
                </Dropdown>
                {/* {card?.archive?.includes(state?.user?._id) ? (
                  <Dropdown
                    menu={{
                      items: cardUnArchiveOptions,
                    }}
                    placement="bottomLeft"
                    trigger={['click']}
                  >
                    <p>
                      <HiOutlineDotsVertical />
                    </p>
                  </Dropdown>
                ) : (
                  // <Table.Options
                  //   onUnarchive={() =>
                  //     handleArchive({
                  //       id: card?._id,
                  //       archiveOrder,
                  //       user: state?.user,
                  //       card,
                  //       apiSlice,
                  //       dispatchFn,
                  //     })
                  //   }
                  //   className={`!py-0 !px-0 mt-1 !-bottom-0`}
                  // />
                  <Dropdown
                    menu={{
                      items: cardArchiveOptions,
                    }}
                    placement="bottomLeft"
                    trigger={['click']}
                  >
                    <HiOutlineDotsVertical className="text-lg cursor-pointer" />
                  </Dropdown>
                  // <Table.Options
                  //   onArchive={() =>
                  //     handleArchive({
                  //       id: card?._id,
                  //       archiveOrder,
                  //       user: state?.user,
                  //       card,
                  //       apiSlice,
                  //       dispatchFn,
                  //     })
                  //   }
                  //   className={`!py-0 !px-0 mt-1 !-bottom-0`}
                  // />
                )} */}
              </div>
            </div>
            {/* {userModal && (
              <AssignUserModal
                setUserModal={setUserModal}
                card={card}
                user={state?.user}
                employees={employees}
              />
            )} */}
            <UserAssignModalV2
              userModal={userModal}
              setUserModal={setUserModal}
              card={card}
              selectedUsers={selectedUsers}
              setSelectedUsers={setSelectedUsers}
            />
            <TaskReminderModal
              reminderModal={reminderModal}
              setReminderModal={setReminderModal}
              card={card}
            />
            {showSelectTags && (
              <Tags
                setShowSelectTags={setShowSelectTags}
                setSelectedTags={setSelectedTags}
                selectedTags={selectedTags}
                defaultParam={defaultParam}
                column={column}
                card={card}
                setInitialScrollIndex={setInitialScrollIndex}
              />
            )}
            {/* {reminderModal && (
              <ReminderModal
                setReminderModal={setReminderModal}
                user={state?.user}
                card={card}
              />
            )} */}
          </div>
        </>
      )}
    </>
  );
};

export default CardV2;
