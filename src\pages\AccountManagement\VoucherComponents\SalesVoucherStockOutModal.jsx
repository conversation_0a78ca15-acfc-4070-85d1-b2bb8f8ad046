import { Input<PERSON><PERSON><PERSON>, Modal, Spin } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import SelectV2 from '../../../components/global/components/SelectV2';
import { useGetProductsDetailsByProductNamesQuery } from '../../../slices/productApiSlice';

const SalesVoucherStockOutModal = ({
  open,
  onCancel,
  onSkip,
  onSubmit,
  items = [],
}) => {
  const productNames = useMemo(() => {
    const names = (items || [])
      .map((it) => it?.productName || it?.productname || it?.itemName || null)
      .filter(Boolean);
    return Array.from(new Set(names));
  }, [items]);

  const { data: productsDetails, isLoading: productsLoading } =
    useGetProductsDetailsByProductNamesQuery(
      { productNames },
      { skip: !open || productNames.length === 0 }
    );

  const initialRows = useMemo(() => {
    return (items || []).map((it, idx) => {
      const name = it?.productName || it?.productname || it?.itemName || '';
      const qty = parseFloat(it?.quantity) || 0;
      return {
        key: it?.key || `${idx}`,
        name,
        store: '',
        quantity: qty,
        availableQuantity: null,
      };
    });
  }, [items]);

  const [rows, setRows] = useState(initialRows);
  useEffect(() => setRows(initialRows), [initialRows]);

  const getStoreOptionsFor = (productName) => {
    if (!productName || !productsDetails?.products) return [];
    const product = productsDetails.products.find(
      (p) => p.productName === productName
    );
    if (!product?.stores) return [];

    return product.stores.map((store) => ({
      value: store._id,
      name: store.name,
    }));
  };

  const getAvailableQuantity = (productName, storeId) => {
    if (!productName || !storeId || !productsDetails?.products) return null;
    const product = productsDetails.products.find(
      (p) => p.productName === productName
    );
    if (!product?.stores) return null;

    const store = product.stores.find((s) => s._id === storeId);
    return store?.availableQuantity ?? null;
  };

  const handleStoreChange = (rowKey, storeId) => {
    setRows((prev) =>
      prev.map((r) => {
        if (r.key === rowKey) {
          const availableQuantity = getAvailableQuantity(r.name, storeId);
          return { ...r, store: storeId, availableQuantity };
        }
        return r;
      })
    );
  };

  const handleQuantityChange = (rowKey, qty) => {
    setRows((prev) =>
      prev.map((r) => (r.key === rowKey ? { ...r, quantity: qty || 0 } : r))
    );
  };

  const RowLine = ({ row }) => {
    const isQuantityExceeded =
      typeof row.availableQuantity === 'number' &&
      (row?.quantity || 0) > row.availableQuantity;
    const hasNoStock = row.availableQuantity === 0;

    return (
      <div className="flex flex-col md:flex-row items-stretch md:items-center gap-2 p-3">
        <div className="flex-1">
          <div className="text-xs text-gray-500">Item</div>
          <div className="text-sm font-medium text-gray-900 break-words">
            {row?.name || '—'}
          </div>
        </div>
        <div className="w-full md:w-64">
          <div className="text-xs text-gray-500 mb-1">Store</div>
          {productsLoading ? (
            <Spin size="small" />
          ) : (
            <SelectV2
              options={getStoreOptionsFor(row?.name)}
              value={row?.store || ''}
              onChange={(e) =>
                handleStoreChange(row.key, e?.target?.value || '')
              }
              placeholder="Select store"
              className={!row?.store ? 'border-red-200' : ''}
            />
          )}
          {!row?.store && (
            <div className="text-xs text-red-500 mt-1">
              Store selection required
            </div>
          )}
        </div>
        <div className="w-full md:w-40">
          <div className="text-xs text-gray-500 mb-1">Available</div>
          <div className="text-sm font-semibold text-gray-800">
            {row?.store ? (row.availableQuantity ?? '—') : '—'}
          </div>
        </div>
        <div className="w-full md:w-48">
          <div className="text-xs text-gray-500 mb-1">Stock Out Qty</div>
          <InputNumber
            min={0}
            max={
              typeof row.availableQuantity === 'number'
                ? row.availableQuantity
                : undefined
            }
            className={`w-full ${isQuantityExceeded || hasNoStock ? 'border-red-300' : ''}`}
            value={row?.quantity}
            onChange={(val) => handleQuantityChange(row.key, val)}
            disabled={hasNoStock}
          />
          {isQuantityExceeded && (
            <div className="text-xs text-red-500 mt-1">
              Exceeds available qty ({row.availableQuantity})
            </div>
          )}
          {hasNoStock && row?.store && (
            <div className="text-xs text-red-500 mt-1">No stock available</div>
          )}
          {(row?.quantity || 0) <= 0 && row?.store && !hasNoStock && (
            <div className="text-xs text-red-500 mt-1">
              Quantity must be greater than 0
            </div>
          )}
        </div>
      </div>
    );
  };

  const isValid = useMemo(() => {
    // Check if all rows have stores selected
    const allHaveStores = rows.every((r) => r?.store);
    if (!allHaveStores) return false;

    const allValidQuantities = rows.every((r) => {
      const qty = r?.quantity || 0;
      const available = r?.availableQuantity;

      // Quantity must be greater than 0
      if (qty <= 0) return false;
      if (typeof available === 'number' && qty > available) return false;

      // If available quantity is 0, quantity should be 0
      if (available === 0) return false;

      return true;
    });

    return allValidQuantities;
  }, [rows]);

  const getValidationMessage = () => {
    const missingStores = rows.filter((r) => !r?.store);
    const invalidQuantities = rows.filter((r) => {
      const qty = r?.quantity || 0;
      const available = r?.availableQuantity;
      return (
        qty <= 0 ||
        (typeof available === 'number' && qty > available) ||
        available === 0
      );
    });

    if (missingStores.length > 0) {
      return `Please select stores for all items (${missingStores.length} missing)`;
    }

    if (invalidQuantities.length > 0) {
      return `Please fix quantity issues for ${invalidQuantities.length} item(s)`;
    }

    return '';
  };

  return (
    <Modal
      title="Stock Out from Sales Voucher"
      open={open}
      onCancel={onCancel}
      footer={null}
      width={800}
      destroyOnClose
      centered
    >
      <div className="space-y-3">
        <p className="text-sm text-gray-600">
          Select a store for each item to view available quantity and enter the
          quantity to stock out.
        </p>
        <div className="divide-y border rounded-md">
          {rows.map((row) => (
            <RowLine key={row.key} row={row} />
          ))}
        </div>

        {!isValid && (
          <div className="text-xs text-red-500 bg-red-50 p-2 rounded border">
            {getValidationMessage()}
          </div>
        )}

        <div className="flex justify-end gap-2 pt-2">
          <button
            onClick={onSkip}
            className="px-3 py-1.5 rounded-md border text-sm hover:bg-gray-50"
          >
            Skip & Create Voucher
          </button>
          <button
            onClick={() => onSubmit(rows)}
            disabled={!isValid}
            className={`px-3 py-1.5 rounded-md text-white text-sm ${
              isValid
                ? 'bg-blue-600 hover:bg-blue-700'
                : 'bg-blue-300 cursor-not-allowed'
            }`}
            title={!isValid ? getValidationMessage() : ''}
          >
            Stock Out & Continue
          </button>
        </div>
      </div>
    </Modal>
  );
};

export default SalesVoucherStockOutModal;
