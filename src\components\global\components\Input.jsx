import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';
import { useState } from 'react';
import { DebounceInput } from 'react-debounce-input';

const InputComponent = ({ debounceTimeout = null, innerRef, ...rest }) => {
  if (debounceTimeout) {
    return (
      <DebounceInput
        debounceTimeout={debounceTimeout}
        ref={innerRef}
        {...rest}
      />
    );
  }
  return <input ref={innerRef} {...rest} />;
};

const Input = ({
  type = 'text',
  value,
  onChange,
  placeholder = 'Enter Value',
  required = false,
  disabled = false,
  readOnly = false,
  className = '',
  innerRef,
  checked,
  onBlur,
  onFocus,
  name,
  inputClassname,
  height = '43px',
  debounceTimeout = null,
  ...rest
}) => {
  const [showPassword, setShowPassword] = useState(false);

  return (
    <div className={`relative rounded-lg ${className}`}>
      <InputComponent
        debounceTimeout={debounceTimeout}
        type={showPassword ? 'text' : type}
        value={value}
        checked={checked}
        onChange={onChange}
        required={required}
        placeholder={placeholder}
        disabled={disabled}
        readOnly={readOnly}
        innerRef={innerRef}
        onBlur={onBlur}
        onFocus={onFocus}
        autoComplete="off"
        className={`border ${type === 'check' ? 'rounded-full' : ''} text-left text-black h-[${height}] outline-none border-[#C8CEE1] bg-transparent rounded-[6px] px-[0.4rem] py-[0.25rem] w-full ${inputClassname} disabled:!bg-gray-100`}
        onWheel={(e) => {
          e.target.blur();
          setTimeout(() => e.target.focus(), 100);
        }}
        name={name}
        {...rest}
      />
      {type === 'password' && (
        <>
          {showPassword ? (
            <EyeIcon
              className="absolute top-1/2 -translate-y-1/2 right-[2%] hover:cursor-pointer text-gray-primary h-5 w-5"
              onClick={() => setShowPassword(false)}
            />
          ) : (
            <EyeSlashIcon
              className="absolute top-1/2 -translate-y-1/2 right-[2%] hover:cursor-pointer text-gray-primary h-5 w-5"
              onClick={() => setShowPassword(true)}
            />
          )}
        </>
      )}
    </div>
  );
};

export default Input;
